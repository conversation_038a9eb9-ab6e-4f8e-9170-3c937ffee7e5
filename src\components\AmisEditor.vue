<template>
  <div class="amis-editor-container">
    <div class="editor-header">
      <div class="header-left">
        <h2>页面编辑器</h2>
        <div class="page-info" v-if="currentPageName">
          <span>当前页面: {{ currentPageName }}</span>
        </div>
      </div>
      <div class="header-actions">
        <button @click="showSaveDialog = true" class="btn btn-primary">保存页面</button>
        <button @click="previewPage" class="btn btn-secondary">预览</button>
        <button @click="newPage" class="btn btn-outline">新建页面</button>
      </div>
    </div>
    
    <div class="editor-content">
      <div class="editor-layout">
        <div class="editor-panel">
          <div class="panel-header">
            <h4>JSON 配置编辑器</h4>
            <button @click="formatJson" class="btn btn-small">格式化</button>
          </div>
          <div ref="editorContainer" class="editor-wrapper"></div>
        </div>
        <div class="preview-panel">
          <div class="panel-header">
            <h4>实时预览</h4>
            <button @click="refreshPreview" class="btn btn-small">刷新</button>
          </div>
          <div ref="previewContainer" class="preview-wrapper"></div>
        </div>
      </div>
    </div>

    <!-- 保存对话框 -->
    <div v-if="showSaveDialog" class="modal-overlay" @click="showSaveDialog = false">
      <div class="modal" @click.stop>
        <h3>保存页面</h3>
        <form @submit.prevent="savePageData">
          <div class="form-group">
            <label>页面名称:</label>
            <input 
              v-model="pageForm.name" 
              type="text" 
              required 
              placeholder="请输入页面名称"
            />
          </div>
          <div class="form-group">
            <label>页面描述:</label>
            <textarea 
              v-model="pageForm.description" 
              placeholder="请输入页面描述（可选）"
              rows="3"
            ></textarea>
          </div>
          <div class="form-actions">
            <button type="button" @click="showSaveDialog = false" class="btn btn-secondary">取消</button>
            <button type="submit" class="btn btn-primary">保存</button>
          </div>
        </form>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { loadPage, savePage, generateId } from '../utils/storage.js'

const route = useRoute()
const router = useRouter()

const editorContainer = ref(null)
const previewContainer = ref(null)
const showSaveDialog = ref(false)
const currentPageId = ref(null)
const currentPageName = ref('')

let previewInstance = null

const pageForm = ref({
  name: '',
  description: ''
})

let editor = null

onMounted(async () => {
  await initAmisEditor()
  
  // 如果URL中有id参数，加载对应的页面
  if (route.query.id) {
    loadPageData(route.query.id)
  }
})

onUnmounted(() => {
  if (editor) {
    editor.dispose()
  }
  if (previewInstance && previewInstance.unmount) {
    previewInstance.unmount()
  }
})

async function initAmisEditor() {
  try {
    // 默认的页面配置
    const defaultSchema = {
      type: 'page',
      title: '新页面',
      body: [
        {
          type: 'tpl',
          tpl: '欢迎使用 Amis 编辑器！点击右侧预览按钮查看效果。'
        }
      ]
    }

    // 动态导入Monaco Editor
    const monaco = await import('monaco-editor')

    // 创建Monaco编辑器
    const monacoEditor = monaco.editor.create(editorContainer.value, {
      value: JSON.stringify(defaultSchema, null, 2),
      language: 'json',
      theme: 'vs',
      automaticLayout: true,
      minimap: { enabled: false },
      scrollBeyondLastLine: false,
      fontSize: 14,
      tabSize: 2,
      insertSpaces: true,
      wordWrap: 'on'
    })

    // 添加内容变化监听器，实现自动预览
    let debounceTimer = null
    monacoEditor.onDidChangeModelContent(() => {
      if (debounceTimer) {
        clearTimeout(debounceTimer)
      }
      debounceTimer = setTimeout(() => {
        refreshPreview()
      }, 1000) // 1秒后自动刷新预览
    })

    // 保存编辑器引用
    editor = {
      getSchema: () => {
        try {
          const value = monacoEditor.getValue()
          return JSON.parse(value)
        } catch (e) {
          alert('JSON格式错误，请检查语法: ' + e.message)
          return null
        }
      },
      updateSchema: (schema) => {
        monacoEditor.setValue(JSON.stringify(schema, null, 2))
      },
      dispose: () => {
        monacoEditor.dispose()
      }
    }

    console.log('Monaco Editor initialized successfully')

    // 初始化预览
    setTimeout(() => {
      refreshPreview()
    }, 100)
  } catch (error) {
    console.error('Failed to initialize Editor:', error)
    alert('编辑器初始化失败: ' + error.message)
  }
}

function loadPageData(pageId) {
  try {
    const pageData = loadPage(pageId)
    if (pageData) {
      currentPageId.value = pageId
      currentPageName.value = pageData.name
      pageForm.value.name = pageData.name
      pageForm.value.description = pageData.description || ''

      if (editor && pageData.schema) {
        editor.updateSchema(pageData.schema)
        // 更新预览
        setTimeout(() => {
          refreshPreview()
        }, 100)
      }
    }
  } catch (error) {
    console.error('Failed to load page:', error)
    alert('加载页面失败')
  }
}

function savePageData() {
  if (!editor) {
    alert('编辑器未初始化')
    return
  }

  try {
    const schema = editor.getSchema()
    if (!schema) {
      return
    }

    const pageId = currentPageId.value || generateId()

    const pageData = {
      name: pageForm.value.name,
      description: pageForm.value.description,
      schema: schema
    }

    const success = savePage(pageId, pageData)

    if (success) {
      currentPageId.value = pageId
      currentPageName.value = pageForm.value.name
      showSaveDialog.value = false
      alert('页面保存成功！')
    } else {
      alert('保存页面失败')
    }
  } catch (error) {
    console.error('Failed to save page:', error)
    alert('保存页面失败')
  }
}

function previewPage() {
  if (!currentPageId.value) {
    alert('请先保存页面')
    return
  }
  
  router.push(`/preview/${currentPageId.value}`)
}

function newPage() {
  if (editor) {
    editor.updateSchema({
      type: 'page',
      title: '新页面',
      body: []
    })
  }
  
  currentPageId.value = null
  currentPageName.value = ''
  pageForm.value.name = ''
  pageForm.value.description = ''
}



async function refreshPreview() {
  if (!editor || !previewContainer.value) {
    return
  }

  const schema = editor.getSchema()
  if (!schema) {
    return
  }

  try {
    // 清理之前的实例
    if (previewInstance) {
      if (previewInstance.unmount) {
        previewInstance.unmount()
      } else {
        previewContainer.value.innerHTML = ''
      }
    }

    // 动态导入amis
    const amis = await import('amis')

    // 清空容器
    previewContainer.value.innerHTML = ''

    // 渲染页面
    previewInstance = amis.render(
      schema,
      {},
      {
        theme: 'cxd',
        locale: 'zh-CN'
      }
    )

    // 挂载到容器
    previewContainer.value.appendChild(previewInstance)

    console.log('Preview updated successfully')
  } catch (error) {
    console.error('Failed to update preview:', error)
    previewContainer.value.innerHTML = `<div class="error-message">预览失败: ${error.message}</div>`
  }
}

function formatJson() {
  if (!editor) {
    return
  }

  const schema = editor.getSchema()
  if (schema) {
    editor.updateSchema(schema)
  }
}
</script>

<style scoped>
.amis-editor-container {
  height: calc(100vh - 100px);
  display: flex;
  flex-direction: column;
}

.editor-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 0;
  border-bottom: 1px solid #e8e8e8;
  margin-bottom: 16px;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 16px;
}

.header-left h2 {
  margin: 0;
  color: #333;
}

.page-info {
  color: #666;
  font-size: 14px;
}

.header-actions {
  display: flex;
  gap: 12px;
}

.btn {
  padding: 8px 16px;
  border-radius: 4px;
  border: 1px solid #d9d9d9;
  background: white;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.3s;
}

.btn-primary {
  background: #1890ff;
  color: white;
  border-color: #1890ff;
}

.btn-primary:hover {
  background: #40a9ff;
  border-color: #40a9ff;
}

.btn-secondary {
  background: #f5f5f5;
  color: #333;
}

.btn-secondary:hover {
  background: #fff;
  border-color: #1890ff;
  color: #1890ff;
}

.btn-outline {
  background: transparent;
  color: #1890ff;
  border-color: #1890ff;
}

.btn-outline:hover {
  background: #f0f8ff;
}

.editor-content {
  flex: 1;
  overflow: hidden;
}

.editor-layout {
  display: flex;
  height: 100%;
  gap: 1px;
  background: #e8e8e8;
}

.editor-panel,
.preview-panel {
  flex: 1;
  display: flex;
  flex-direction: column;
  background: white;
}

.panel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 16px;
  background: #f5f5f5;
  border-bottom: 1px solid #e8e8e8;
}

.panel-header h4 {
  margin: 0;
  font-size: 14px;
  color: #333;
}

.btn-small {
  padding: 4px 8px;
  font-size: 12px;
  border: 1px solid #d9d9d9;
  background: white;
  border-radius: 3px;
  cursor: pointer;
  transition: all 0.3s;
}

.btn-small:hover {
  border-color: #1890ff;
  color: #1890ff;
}

.editor-wrapper {
  flex: 1;
  overflow: hidden;
}

.preview-wrapper {
  flex: 1;
  overflow: auto;
  padding: 16px;
}

.error-message {
  color: #ff4d4f;
  padding: 16px;
  background: #fff2f0;
  border: 1px solid #ffccc7;
  border-radius: 4px;
  margin: 16px;
}

.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.modal {
  background: white;
  border-radius: 8px;
  padding: 24px;
  width: 90%;
  max-width: 500px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.modal h3 {
  margin: 0 0 20px 0;
  color: #333;
}

.form-group {
  margin-bottom: 16px;
}

.form-group label {
  display: block;
  margin-bottom: 4px;
  color: #333;
  font-weight: 500;
}

.form-group input,
.form-group textarea {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid #d9d9d9;
  border-radius: 4px;
  font-size: 14px;
}

.form-group input:focus,
.form-group textarea:focus {
  outline: none;
  border-color: #1890ff;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
}

.form-actions {
  display: flex;
  gap: 12px;
  justify-content: flex-end;
  margin-top: 24px;
}
</style>
