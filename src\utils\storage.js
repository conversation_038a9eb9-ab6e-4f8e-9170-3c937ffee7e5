/**
 * 本地存储工具类
 * 用于管理页面配置的保存和加载
 */

const STORAGE_PREFIX = 'amis-page-'

/**
 * 保存页面配置
 * @param {string} id 页面ID
 * @param {object} pageData 页面数据
 */
export function savePage(id, pageData) {
  try {
    const data = {
      ...pageData,
      id,
      updatedAt: new Date().toISOString()
    }
    localStorage.setItem(`${STORAGE_PREFIX}${id}`, JSON.stringify(data))
    return true
  } catch (error) {
    console.error('Failed to save page:', error)
    return false
  }
}

/**
 * 加载页面配置
 * @param {string} id 页面ID
 * @returns {object|null} 页面数据
 */
export function loadPage(id) {
  try {
    const data = localStorage.getItem(`${STORAGE_PREFIX}${id}`)
    return data ? JSON.parse(data) : null
  } catch (error) {
    console.error('Failed to load page:', error)
    return null
  }
}

/**
 * 删除页面配置
 * @param {string} id 页面ID
 */
export function deletePage(id) {
  try {
    localStorage.removeItem(`${STORAGE_PREFIX}${id}`)
    return true
  } catch (error) {
    console.error('Failed to delete page:', error)
    return false
  }
}

/**
 * 获取所有已保存的页面
 * @returns {array} 页面列表
 */
export function getAllPages() {
  const pages = []
  
  try {
    for (let i = 0; i < localStorage.length; i++) {
      const key = localStorage.key(i)
      if (key && key.startsWith(STORAGE_PREFIX)) {
        const pageData = JSON.parse(localStorage.getItem(key))
        pages.push({
          id: key.replace(STORAGE_PREFIX, ''),
          name: pageData.name || '未命名页面',
          description: pageData.description,
          updatedAt: pageData.updatedAt
        })
      }
    }
    
    // 按更新时间排序
    return pages.sort((a, b) => new Date(b.updatedAt) - new Date(a.updatedAt))
  } catch (error) {
    console.error('Failed to get all pages:', error)
    return []
  }
}

/**
 * 生成唯一ID
 * @returns {string} 唯一ID
 */
export function generateId() {
  return Date.now().toString(36) + Math.random().toString(36).substring(2)
}

/**
 * 验证页面配置
 * @param {object} schema 页面配置
 * @returns {boolean} 是否有效
 */
export function validateSchema(schema) {
  if (!schema || typeof schema !== 'object') {
    return false
  }
  
  // 基本验证：必须有type字段
  if (!schema.type) {
    return false
  }
  
  return true
}

/**
 * 清理所有页面数据
 */
export function clearAllPages() {
  try {
    const keysToRemove = []
    for (let i = 0; i < localStorage.length; i++) {
      const key = localStorage.key(i)
      if (key && key.startsWith(STORAGE_PREFIX)) {
        keysToRemove.push(key)
      }
    }
    
    keysToRemove.forEach(key => localStorage.removeItem(key))
    return true
  } catch (error) {
    console.error('Failed to clear all pages:', error)
    return false
  }
}

/**
 * 导出页面配置为JSON文件
 * @param {string} id 页面ID
 */
export function exportPage(id) {
  const pageData = loadPage(id)
  if (!pageData) {
    throw new Error('页面不存在')
  }
  
  const dataStr = JSON.stringify(pageData, null, 2)
  const dataBlob = new Blob([dataStr], { type: 'application/json' })
  
  const link = document.createElement('a')
  link.href = URL.createObjectURL(dataBlob)
  link.download = `${pageData.name || 'page'}.json`
  link.click()
  
  URL.revokeObjectURL(link.href)
}

/**
 * 从JSON文件导入页面配置
 * @param {File} file JSON文件
 * @returns {Promise<object>} 页面数据
 */
export function importPage(file) {
  return new Promise((resolve, reject) => {
    const reader = new FileReader()
    
    reader.onload = (e) => {
      try {
        const pageData = JSON.parse(e.target.result)
        
        if (!validateSchema(pageData.schema)) {
          reject(new Error('无效的页面配置'))
          return
        }
        
        resolve(pageData)
      } catch (error) {
        reject(new Error('JSON格式错误'))
      }
    }
    
    reader.onerror = () => {
      reject(new Error('文件读取失败'))
    }
    
    reader.readAsText(file)
  })
}
