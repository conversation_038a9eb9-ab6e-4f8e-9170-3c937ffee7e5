<template>
  <div class="amis-preview-container">
    <div class="preview-header">
      <div class="header-left">
        <h2>页面预览</h2>
        <div class="page-info" v-if="currentPage">
          <span>{{ currentPage.name }}</span>
          <span v-if="currentPage.description" class="description">{{ currentPage.description }}</span>
        </div>
      </div>
      <div class="header-actions">
        <button @click="editPage" class="btn btn-primary" v-if="currentPage">编辑页面</button>
        <button @click="goHome" class="btn btn-secondary">返回首页</button>
      </div>
    </div>

    <div class="preview-content">
      <div v-if="!currentPage" class="no-page">
        <h3>没有找到页面</h3>
        <p>请选择一个有效的页面进行预览</p>
        <router-link to="/" class="btn btn-primary">返回首页</router-link>
      </div>
      <div v-else-if="!currentPage.schema" class="no-schema">
        <h3>页面配置为空</h3>
        <p>该页面还没有配置内容</p>
        <button @click="editPage" class="btn btn-primary">去编辑</button>
      </div>
      <div v-else ref="previewContainer" class="preview-wrapper"></div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, watch } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { loadPage } from '../utils/storage.js'

const props = defineProps({
  id: String
})

const route = useRoute()
const router = useRouter()

const previewContainer = ref(null)
const currentPage = ref(null)

let amisInstance = null

onMounted(async () => {
  await loadPageData()
})

onUnmounted(() => {
  if (amisInstance) {
    amisInstance.unmount()
  }
})

watch(() => props.id, () => {
  loadPageData()
})

watch(() => route.params.id, () => {
  loadPageData()
})

async function loadPageData() {
  const pageId = props.id || route.params.id

  if (!pageId) {
    currentPage.value = null
    return
  }

  try {
    const pageData = loadPage(pageId)
    if (pageData) {
      currentPage.value = pageData
      await renderPage()
    } else {
      currentPage.value = null
    }
  } catch (error) {
    console.error('Failed to load page:', error)
    currentPage.value = null
  }
}

async function renderPage() {
  if (!currentPage.value || !currentPage.value.schema || !previewContainer.value) {
    return
  }

  try {
    // 清理之前的实例
    if (amisInstance) {
      if (amisInstance.unmount) {
        amisInstance.unmount()
      } else if (previewContainer.value) {
        previewContainer.value.innerHTML = ''
      }
    }

    // 动态导入amis
    const amis = await import('amis')

    // 清空容器
    previewContainer.value.innerHTML = ''

    // 渲染页面
    amisInstance = amis.render(
      currentPage.value.schema,
      {
        // 可以在这里传入数据和上下文
      },
      {
        theme: 'cxd',
        locale: 'zh-CN'
      }
    )

    // 挂载到容器
    previewContainer.value.appendChild(amisInstance)

    console.log('Page rendered successfully')
  } catch (error) {
    console.error('Failed to render page:', error)
    alert('页面渲染失败: ' + error.message)
  }
}

function editPage() {
  if (currentPage.value) {
    router.push(`/editor?id=${currentPage.value.id}`)
  }
}

function goHome() {
  router.push('/')
}
</script>

<style scoped>
.amis-preview-container {
  height: calc(100vh - 100px);
  display: flex;
  flex-direction: column;
}

.preview-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 0;
  border-bottom: 1px solid #e8e8e8;
  margin-bottom: 16px;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 16px;
}

.header-left h2 {
  margin: 0;
  color: #333;
}

.page-info {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.page-info > span:first-child {
  font-weight: 500;
  color: #333;
}

.description {
  font-size: 12px;
  color: #666;
}

.header-actions {
  display: flex;
  gap: 12px;
}

.btn {
  padding: 8px 16px;
  border-radius: 4px;
  border: 1px solid #d9d9d9;
  background: white;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.3s;
  text-decoration: none;
  display: inline-block;
}

.btn-primary {
  background: #1890ff;
  color: white;
  border-color: #1890ff;
}

.btn-primary:hover {
  background: #40a9ff;
  border-color: #40a9ff;
}

.btn-secondary {
  background: #f5f5f5;
  color: #333;
}

.btn-secondary:hover {
  background: #fff;
  border-color: #1890ff;
  color: #1890ff;
}

.preview-content {
  flex: 1;
  overflow: auto;
}

.preview-wrapper {
  width: 100%;
  height: 100%;
}

.no-page,
.no-schema {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  text-align: center;
  color: #666;
}

.no-page h3,
.no-schema h3 {
  margin-bottom: 16px;
  color: #333;
}

.no-page p,
.no-schema p {
  margin-bottom: 24px;
  font-size: 16px;
}
</style>

<style>
/* 全局样式，用于amis组件 */
.amis-preview-container .cxd-Page {
  padding: 0;
}

.amis-preview-container .cxd-Page-content {
  padding: 20px;
}
</style>
