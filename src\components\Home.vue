<template>
  <div class="home">
    <div class="hero">
      <h1>Amis 可视化页面编辑器</h1>
      <p>使用 Amis Editor 快速创建和编辑页面配置</p>
      <div class="actions">
        <router-link to="/editor" class="btn btn-primary">开始编辑</router-link>
        <router-link to="/preview" class="btn btn-secondary">查看预览</router-link>
      </div>
    </div>

    <div class="features">
      <div class="feature-card">
        <h3>🎨 可视化编辑</h3>
        <p>拖拽式界面设计，无需编写代码即可创建复杂页面</p>
      </div>
      <div class="feature-card">
        <h3>💾 配置保存</h3>
        <p>支持保存和加载页面配置，方便管理多个页面</p>
      </div>
      <div class="feature-card">
        <h3>👀 实时预览</h3>
        <p>实时查看页面效果，所见即所得的编辑体验</p>
      </div>
    </div>

    <div class="saved-pages" v-if="savedPages.length > 0">
      <h2>已保存的页面</h2>
      <div class="pages-grid">
        <div 
          v-for="page in savedPages" 
          :key="page.id" 
          class="page-card"
          @click="previewPage(page.id)"
        >
          <h4>{{ page.name }}</h4>
          <p>{{ page.description || '无描述' }}</p>
          <div class="page-actions">
            <button @click.stop="editPage(page.id)" class="btn-small">编辑</button>
            <button @click.stop="deletePage(page.id)" class="btn-small btn-danger">删除</button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { getAllPages, deletePage as deletePageFromStorage, savePage } from '../utils/storage.js'

const router = useRouter()
const savedPages = ref([])

onMounted(() => {
  loadSavedPages()
  createSamplePages()
})

function loadSavedPages() {
  savedPages.value = getAllPages()
}

function previewPage(id) {
  router.push(`/preview/${id}`)
}

function editPage(id) {
  router.push(`/editor?id=${id}`)
}

function deletePage(id) {
  if (confirm('确定要删除这个页面吗？')) {
    deletePageFromStorage(id)
    loadSavedPages()
  }
}

function createSamplePages() {
  // 检查是否已经有示例页面
  if (savedPages.value.some(page => page.id === 'sample-form')) {
    return
  }

  // 创建示例表单页面
  const formPage = {
    id: 'sample-form',
    name: '示例表单页面',
    description: '包含各种表单组件的示例页面',
    schema: {
      type: 'page',
      title: '用户信息表单',
      body: [
        {
          type: 'form',
          title: '用户注册',
          api: '/api/register',
          body: [
            {
              type: 'input-text',
              name: 'username',
              label: '用户名',
              required: true,
              placeholder: '请输入用户名'
            },
            {
              type: 'input-email',
              name: 'email',
              label: '邮箱',
              required: true,
              placeholder: '请输入邮箱地址'
            },
            {
              type: 'input-password',
              name: 'password',
              label: '密码',
              required: true,
              placeholder: '请输入密码'
            },
            {
              type: 'select',
              name: 'gender',
              label: '性别',
              options: [
                { label: '男', value: 'male' },
                { label: '女', value: 'female' }
              ]
            },
            {
              type: 'textarea',
              name: 'bio',
              label: '个人简介',
              placeholder: '请输入个人简介'
            }
          ]
        }
      ]
    },
    updatedAt: new Date().toISOString()
  }

  // 创建示例展示页面
  const displayPage = {
    id: 'sample-display',
    name: '示例展示页面',
    description: '包含各种展示组件的示例页面',
    schema: {
      type: 'page',
      title: '数据展示',
      body: [
        {
          type: 'alert',
          level: 'info',
          body: '这是一个信息提示框'
        },
        {
          type: 'divider'
        },
        {
          type: 'tpl',
          tpl: '<h3>欢迎使用 Amis</h3><p>这是一个模板组件，可以显示 HTML 内容。</p>'
        },
        {
          type: 'card',
          header: {
            title: '卡片标题',
            subTitle: '卡片副标题'
          },
          body: '这是卡片的内容区域，可以放置任何组件。'
        },
        {
          type: 'grid',
          columns: [
            {
              type: 'tpl',
              tpl: '左侧内容',
              className: 'bg-light p-3'
            },
            {
              type: 'tpl',
              tpl: '右侧内容',
              className: 'bg-light p-3'
            }
          ]
        }
      ]
    },
    updatedAt: new Date().toISOString()
  }

  // 保存示例页面
  savePage('sample-form', formPage)
  savePage('sample-display', displayPage)
}
</script>

<style scoped>
.home {
  padding: 40px 0;
}

.hero {
  text-align: center;
  margin-bottom: 60px;
}

.hero h1 {
  font-size: 48px;
  color: #333;
  margin-bottom: 16px;
  font-weight: 700;
}

.hero p {
  font-size: 20px;
  color: #666;
  margin-bottom: 32px;
}

.actions {
  display: flex;
  gap: 16px;
  justify-content: center;
}

.btn {
  padding: 12px 24px;
  border-radius: 6px;
  text-decoration: none;
  font-weight: 500;
  transition: all 0.3s;
  border: none;
  cursor: pointer;
  font-size: 16px;
}

.btn-primary {
  background: #1890ff;
  color: white;
}

.btn-primary:hover {
  background: #40a9ff;
  transform: translateY(-2px);
}

.btn-secondary {
  background: #f5f5f5;
  color: #333;
  border: 1px solid #d9d9d9;
}

.btn-secondary:hover {
  background: #fff;
  border-color: #1890ff;
  color: #1890ff;
}

.features {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 24px;
  margin-bottom: 60px;
}

.feature-card {
  background: white;
  padding: 32px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  text-align: center;
}

.feature-card h3 {
  font-size: 24px;
  margin-bottom: 16px;
  color: #333;
}

.feature-card p {
  color: #666;
  line-height: 1.6;
}

.saved-pages {
  background: white;
  padding: 32px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.saved-pages h2 {
  margin-bottom: 24px;
  color: #333;
}

.pages-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 16px;
}

.page-card {
  border: 1px solid #e8e8e8;
  border-radius: 6px;
  padding: 20px;
  cursor: pointer;
  transition: all 0.3s;
}

.page-card:hover {
  border-color: #1890ff;
  box-shadow: 0 4px 12px rgba(24, 144, 255, 0.15);
}

.page-card h4 {
  margin-bottom: 8px;
  color: #333;
}

.page-card p {
  color: #666;
  margin-bottom: 16px;
  font-size: 14px;
}

.page-actions {
  display: flex;
  gap: 8px;
}

.btn-small {
  padding: 4px 12px;
  font-size: 12px;
  border: 1px solid #d9d9d9;
  background: white;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.3s;
}

.btn-small:hover {
  border-color: #1890ff;
  color: #1890ff;
}

.btn-danger:hover {
  border-color: #ff4d4f;
  color: #ff4d4f;
}
</style>
