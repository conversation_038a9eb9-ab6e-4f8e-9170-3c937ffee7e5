# 部署指南

## 本地开发

### 环境要求
- Node.js 16+ 
- pnpm (推荐) 或 npm/yarn

### 开发步骤
```bash
# 1. 克隆项目
git clone <repository-url>
cd test-amis

# 2. 安装依赖
pnpm install

# 3. 启动开发服务器
pnpm dev

# 4. 打开浏览器访问
# http://localhost:5174
```

## 生产构建

### 构建应用
```bash
# 构建生产版本
pnpm build

# 预览构建结果
pnpm preview
```

构建完成后，`dist` 目录包含所有静态文件。

## 部署选项

### 1. 静态文件服务器
将 `dist` 目录上传到任何静态文件服务器：
- Nginx
- Apache
- GitHub Pages
- Netlify
- Vercel

### 2. Nginx 配置示例
```nginx
server {
    listen 80;
    server_name your-domain.com;
    root /path/to/dist;
    index index.html;

    location / {
        try_files $uri $uri/ /index.html;
    }

    # 缓存静态资源
    location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
    }
}
```

### 3. GitHub Pages 部署
```bash
# 1. 构建应用
pnpm build

# 2. 创建 gh-pages 分支
git checkout -b gh-pages

# 3. 复制构建文件
cp -r dist/* .

# 4. 提交并推送
git add .
git commit -m "Deploy to GitHub Pages"
git push origin gh-pages
```

### 4. Vercel 部署
1. 连接 GitHub 仓库到 Vercel
2. 设置构建命令：`pnpm build`
3. 设置输出目录：`dist`
4. 自动部署

### 5. Netlify 部署
1. 拖拽 `dist` 目录到 Netlify
2. 或连接 Git 仓库自动部署
3. 构建设置：
   - Build command: `pnpm build`
   - Publish directory: `dist`

## 环境变量

如果需要配置不同环境的变量，创建以下文件：

### `.env.development`
```
VITE_API_BASE_URL=http://localhost:3000
VITE_APP_TITLE=Amis Editor (Dev)
```

### `.env.production`
```
VITE_API_BASE_URL=https://api.yourdomain.com
VITE_APP_TITLE=Amis Editor
```

## Docker 部署

### Dockerfile
```dockerfile
# 构建阶段
FROM node:18-alpine as builder

WORKDIR /app
COPY package*.json ./
RUN npm ci --only=production

COPY . .
RUN npm run build

# 生产阶段
FROM nginx:alpine

COPY --from=builder /app/dist /usr/share/nginx/html
COPY nginx.conf /etc/nginx/nginx.conf

EXPOSE 80
CMD ["nginx", "-g", "daemon off;"]
```

### docker-compose.yml
```yaml
version: '3.8'
services:
  amis-editor:
    build: .
    ports:
      - "80:80"
    restart: unless-stopped
```

### 构建和运行
```bash
# 构建镜像
docker build -t amis-editor .

# 运行容器
docker run -p 80:80 amis-editor

# 或使用 docker-compose
docker-compose up -d
```

## 性能优化

### 1. 代码分割
Vite 自动进行代码分割，无需额外配置。

### 2. 资源压缩
```javascript
// vite.config.js
import { defineConfig } from 'vite'
import { resolve } from 'path'

export default defineConfig({
  build: {
    rollupOptions: {
      output: {
        manualChunks: {
          vendor: ['vue', 'vue-router'],
          amis: ['amis'],
          monaco: ['monaco-editor']
        }
      }
    }
  }
})
```

### 3. CDN 加速
```html
<!-- 在 index.html 中使用 CDN -->
<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/amis@latest/lib/themes/cxd.css">
<script src="https://cdn.jsdelivr.net/npm/amis@latest/lib/index.js"></script>
```

## 监控和分析

### 1. 添加 Google Analytics
```html
<!-- 在 index.html 中添加 -->
<script async src="https://www.googletagmanager.com/gtag/js?id=GA_TRACKING_ID"></script>
<script>
  window.dataLayer = window.dataLayer || [];
  function gtag(){dataLayer.push(arguments);}
  gtag('js', new Date());
  gtag('config', 'GA_TRACKING_ID');
</script>
```

### 2. 错误监控
可以集成 Sentry 等错误监控服务。

## 故障排除

### 常见问题
1. **构建失败**: 检查 Node.js 版本和依赖安装
2. **路由不工作**: 确保服务器配置了 SPA 回退
3. **静态资源 404**: 检查 base 路径配置
4. **内存不足**: 增加 Node.js 内存限制

### 调试命令
```bash
# 检查依赖
pnpm list

# 清理缓存
pnpm store prune

# 重新安装依赖
rm -rf node_modules pnpm-lock.yaml
pnpm install
```
