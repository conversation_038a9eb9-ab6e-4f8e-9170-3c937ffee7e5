import { createApp } from 'vue'
import { createRouter, createWebHistory } from 'vue-router'
import './style.css'
// 导入 Amis 样式
import 'amis/lib/themes/cxd.css'
import 'amis/lib/helper.css'
import 'amis/sdk/iconfont.css'

import App from './App.vue'
import AmisEditor from './components/AmisEditor.vue'
import AmisPreview from './components/AmisPreview.vue'
import Home from './components/Home.vue'

const routes = [
  { path: '/', component: Home },
  { path: '/editor', component: AmisEditor },
  { path: '/preview/:id?', component: AmisPreview, props: true }
]

const router = createRouter({
  history: createWebHistory(),
  routes
})

const app = createApp(App)
app.use(router)
app.mount('#app')
