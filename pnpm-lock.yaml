lockfileVersion: '9.0'

settings:
  autoInstallPeers: true
  excludeLinksFromLockfile: false

importers:

  .:
    dependencies:
      amis:
        specifier: ^6.13.0
        version: 6.13.0(@types/react@19.1.12)(amis-core@6.13.0(@types/react@19.1.12)(amis-formula@6.13.0)(react-dom@17.0.2(react@16.14.0))(react-is@18.3.1)(react@16.14.0))(amis-ui@6.13.0(@types/react@19.1.12)(amis-core@6.13.0(@types/react@19.1.12)(amis-formula@6.13.0)(react-dom@17.0.2(react@16.14.0))(react-is@18.3.1)(react@16.14.0))(amis-formula@6.13.0)(react-dom@17.0.2(react@16.14.0))(react@16.14.0))(office-viewer@0.3.14(echarts@5.5.1))(react-dom@17.0.2(react@16.14.0))(react@16.14.0)
      amis-editor:
        specifier: ^6.13.0
        version: 6.13.0(714e4a24ab16266dd62b4510cbf1985d)
      i18n-runtime:
        specifier: 1.0.10
        version: 1.0.10(amis-core@6.13.0(@types/react@19.1.12)(amis-formula@6.13.0)(react-dom@17.0.2(react@16.14.0))(react-is@18.3.1)(react@16.14.0))
      monaco-editor:
        specifier: ^0.52.2
        version: 0.52.2
      vue:
        specifier: ^3.5.18
        version: 3.5.21
      vue-router:
        specifier: ^4.5.1
        version: 4.5.1(vue@3.5.21)
    devDependencies:
      '@vitejs/plugin-vue':
        specifier: ^6.0.1
        version: 6.0.1(vite@7.1.4)(vue@3.5.21)
      vite:
        specifier: ^7.1.2
        version: 7.1.4

packages:

  '@babel/helper-string-parser@7.27.1':
    resolution: {integrity: sha512-qMlSxKbpRlAridDExk92nSobyDdpPijUq2DW6oDnUqd0iOGxmQjyqhMIihI9+zv4LPyZdRje2cavWPbCbWm3eA==}
    engines: {node: '>=6.9.0'}

  '@babel/helper-validator-identifier@7.27.1':
    resolution: {integrity: sha512-D2hP9eA+Sqx1kBZgzxZh0y1trbuU+JoDkiEwqhQ36nodYqJwyEIhPSdMNd7lOm/4io72luTPWH20Yda0xOuUow==}
    engines: {node: '>=6.9.0'}

  '@babel/parser@7.28.3':
    resolution: {integrity: sha512-7+Ey1mAgYqFAx2h0RuoxcQT5+MlG3GTV0TQrgr7/ZliKsm/MNDxVVutlWaziMq7wJNAz8MTqz55XLpWvva6StA==}
    engines: {node: '>=6.0.0'}
    hasBin: true

  '@babel/runtime@7.28.4':
    resolution: {integrity: sha512-Q/N6JNWvIvPnLDvjlE1OUBLPQHH6l3CltCEsHIujp45zQUSSh8K+gHnaEX45yAT1nyngnINhvWtzN+Nb9D8RAQ==}
    engines: {node: '>=6.9.0'}

  '@babel/types@7.28.2':
    resolution: {integrity: sha512-ruv7Ae4J5dUYULmeXw1gmb7rYRz57OWCPM57pHojnLq/3Z1CK2lNSLTCVjxVk1F/TZHwOZZrOWi0ur95BbLxNQ==}
    engines: {node: '>=6.9.0'}

  '@esbuild/aix-ppc64@0.25.9':
    resolution: {integrity: sha512-OaGtL73Jck6pBKjNIe24BnFE6agGl+6KxDtTfHhy1HmhthfKouEcOhqpSL64K4/0WCtbKFLOdzD/44cJ4k9opA==}
    engines: {node: '>=18'}
    cpu: [ppc64]
    os: [aix]

  '@esbuild/android-arm64@0.25.9':
    resolution: {integrity: sha512-IDrddSmpSv51ftWslJMvl3Q2ZT98fUSL2/rlUXuVqRXHCs5EUF1/f+jbjF5+NG9UffUDMCiTyh8iec7u8RlTLg==}
    engines: {node: '>=18'}
    cpu: [arm64]
    os: [android]

  '@esbuild/android-arm@0.25.9':
    resolution: {integrity: sha512-5WNI1DaMtxQ7t7B6xa572XMXpHAaI/9Hnhk8lcxF4zVN4xstUgTlvuGDorBguKEnZO70qwEcLpfifMLoxiPqHQ==}
    engines: {node: '>=18'}
    cpu: [arm]
    os: [android]

  '@esbuild/android-x64@0.25.9':
    resolution: {integrity: sha512-I853iMZ1hWZdNllhVZKm34f4wErd4lMyeV7BLzEExGEIZYsOzqDWDf+y082izYUE8gtJnYHdeDpN/6tUdwvfiw==}
    engines: {node: '>=18'}
    cpu: [x64]
    os: [android]

  '@esbuild/darwin-arm64@0.25.9':
    resolution: {integrity: sha512-XIpIDMAjOELi/9PB30vEbVMs3GV1v2zkkPnuyRRURbhqjyzIINwj+nbQATh4H9GxUgH1kFsEyQMxwiLFKUS6Rg==}
    engines: {node: '>=18'}
    cpu: [arm64]
    os: [darwin]

  '@esbuild/darwin-x64@0.25.9':
    resolution: {integrity: sha512-jhHfBzjYTA1IQu8VyrjCX4ApJDnH+ez+IYVEoJHeqJm9VhG9Dh2BYaJritkYK3vMaXrf7Ogr/0MQ8/MeIefsPQ==}
    engines: {node: '>=18'}
    cpu: [x64]
    os: [darwin]

  '@esbuild/freebsd-arm64@0.25.9':
    resolution: {integrity: sha512-z93DmbnY6fX9+KdD4Ue/H6sYs+bhFQJNCPZsi4XWJoYblUqT06MQUdBCpcSfuiN72AbqeBFu5LVQTjfXDE2A6Q==}
    engines: {node: '>=18'}
    cpu: [arm64]
    os: [freebsd]

  '@esbuild/freebsd-x64@0.25.9':
    resolution: {integrity: sha512-mrKX6H/vOyo5v71YfXWJxLVxgy1kyt1MQaD8wZJgJfG4gq4DpQGpgTB74e5yBeQdyMTbgxp0YtNj7NuHN0PoZg==}
    engines: {node: '>=18'}
    cpu: [x64]
    os: [freebsd]

  '@esbuild/linux-arm64@0.25.9':
    resolution: {integrity: sha512-BlB7bIcLT3G26urh5Dmse7fiLmLXnRlopw4s8DalgZ8ef79Jj4aUcYbk90g8iCa2467HX8SAIidbL7gsqXHdRw==}
    engines: {node: '>=18'}
    cpu: [arm64]
    os: [linux]

  '@esbuild/linux-arm@0.25.9':
    resolution: {integrity: sha512-HBU2Xv78SMgaydBmdor38lg8YDnFKSARg1Q6AT0/y2ezUAKiZvc211RDFHlEZRFNRVhcMamiToo7bDx3VEOYQw==}
    engines: {node: '>=18'}
    cpu: [arm]
    os: [linux]

  '@esbuild/linux-ia32@0.25.9':
    resolution: {integrity: sha512-e7S3MOJPZGp2QW6AK6+Ly81rC7oOSerQ+P8L0ta4FhVi+/j/v2yZzx5CqqDaWjtPFfYz21Vi1S0auHrap3Ma3A==}
    engines: {node: '>=18'}
    cpu: [ia32]
    os: [linux]

  '@esbuild/linux-loong64@0.25.9':
    resolution: {integrity: sha512-Sbe10Bnn0oUAB2AalYztvGcK+o6YFFA/9829PhOCUS9vkJElXGdphz0A3DbMdP8gmKkqPmPcMJmJOrI3VYB1JQ==}
    engines: {node: '>=18'}
    cpu: [loong64]
    os: [linux]

  '@esbuild/linux-mips64el@0.25.9':
    resolution: {integrity: sha512-YcM5br0mVyZw2jcQeLIkhWtKPeVfAerES5PvOzaDxVtIyZ2NUBZKNLjC5z3/fUlDgT6w89VsxP2qzNipOaaDyA==}
    engines: {node: '>=18'}
    cpu: [mips64el]
    os: [linux]

  '@esbuild/linux-ppc64@0.25.9':
    resolution: {integrity: sha512-++0HQvasdo20JytyDpFvQtNrEsAgNG2CY1CLMwGXfFTKGBGQT3bOeLSYE2l1fYdvML5KUuwn9Z8L1EWe2tzs1w==}
    engines: {node: '>=18'}
    cpu: [ppc64]
    os: [linux]

  '@esbuild/linux-riscv64@0.25.9':
    resolution: {integrity: sha512-uNIBa279Y3fkjV+2cUjx36xkx7eSjb8IvnL01eXUKXez/CBHNRw5ekCGMPM0BcmqBxBcdgUWuUXmVWwm4CH9kg==}
    engines: {node: '>=18'}
    cpu: [riscv64]
    os: [linux]

  '@esbuild/linux-s390x@0.25.9':
    resolution: {integrity: sha512-Mfiphvp3MjC/lctb+7D287Xw1DGzqJPb/J2aHHcHxflUo+8tmN/6d4k6I2yFR7BVo5/g7x2Monq4+Yew0EHRIA==}
    engines: {node: '>=18'}
    cpu: [s390x]
    os: [linux]

  '@esbuild/linux-x64@0.25.9':
    resolution: {integrity: sha512-iSwByxzRe48YVkmpbgoxVzn76BXjlYFXC7NvLYq+b+kDjyyk30J0JY47DIn8z1MO3K0oSl9fZoRmZPQI4Hklzg==}
    engines: {node: '>=18'}
    cpu: [x64]
    os: [linux]

  '@esbuild/netbsd-arm64@0.25.9':
    resolution: {integrity: sha512-9jNJl6FqaUG+COdQMjSCGW4QiMHH88xWbvZ+kRVblZsWrkXlABuGdFJ1E9L7HK+T0Yqd4akKNa/lO0+jDxQD4Q==}
    engines: {node: '>=18'}
    cpu: [arm64]
    os: [netbsd]

  '@esbuild/netbsd-x64@0.25.9':
    resolution: {integrity: sha512-RLLdkflmqRG8KanPGOU7Rpg829ZHu8nFy5Pqdi9U01VYtG9Y0zOG6Vr2z4/S+/3zIyOxiK6cCeYNWOFR9QP87g==}
    engines: {node: '>=18'}
    cpu: [x64]
    os: [netbsd]

  '@esbuild/openbsd-arm64@0.25.9':
    resolution: {integrity: sha512-YaFBlPGeDasft5IIM+CQAhJAqS3St3nJzDEgsgFixcfZeyGPCd6eJBWzke5piZuZ7CtL656eOSYKk4Ls2C0FRQ==}
    engines: {node: '>=18'}
    cpu: [arm64]
    os: [openbsd]

  '@esbuild/openbsd-x64@0.25.9':
    resolution: {integrity: sha512-1MkgTCuvMGWuqVtAvkpkXFmtL8XhWy+j4jaSO2wxfJtilVCi0ZE37b8uOdMItIHz4I6z1bWWtEX4CJwcKYLcuA==}
    engines: {node: '>=18'}
    cpu: [x64]
    os: [openbsd]

  '@esbuild/openharmony-arm64@0.25.9':
    resolution: {integrity: sha512-4Xd0xNiMVXKh6Fa7HEJQbrpP3m3DDn43jKxMjxLLRjWnRsfxjORYJlXPO4JNcXtOyfajXorRKY9NkOpTHptErg==}
    engines: {node: '>=18'}
    cpu: [arm64]
    os: [openharmony]

  '@esbuild/sunos-x64@0.25.9':
    resolution: {integrity: sha512-WjH4s6hzo00nNezhp3wFIAfmGZ8U7KtrJNlFMRKxiI9mxEK1scOMAaa9i4crUtu+tBr+0IN6JCuAcSBJZfnphw==}
    engines: {node: '>=18'}
    cpu: [x64]
    os: [sunos]

  '@esbuild/win32-arm64@0.25.9':
    resolution: {integrity: sha512-mGFrVJHmZiRqmP8xFOc6b84/7xa5y5YvR1x8djzXpJBSv/UsNK6aqec+6JDjConTgvvQefdGhFDAs2DLAds6gQ==}
    engines: {node: '>=18'}
    cpu: [arm64]
    os: [win32]

  '@esbuild/win32-ia32@0.25.9':
    resolution: {integrity: sha512-b33gLVU2k11nVx1OhX3C8QQP6UHQK4ZtN56oFWvVXvz2VkDoe6fbG8TOgHFxEvqeqohmRnIHe5A1+HADk4OQww==}
    engines: {node: '>=18'}
    cpu: [ia32]
    os: [win32]

  '@esbuild/win32-x64@0.25.9':
    resolution: {integrity: sha512-PPOl1mi6lpLNQxnGoyAfschAodRFYXJ+9fs6WHXz7CSWKbOqiMZsubC+BQsVKuul+3vKLuwTHsS2c2y9EoKwxQ==}
    engines: {node: '>=18'}
    cpu: [x64]
    os: [win32]

  '@fast-csv/format@4.3.5':
    resolution: {integrity: sha512-8iRn6QF3I8Ak78lNAa+Gdl5MJJBM5vRHivFtMRUWINdevNo00K7OXxS2PshawLKTejVwieIlPmK5YlLu6w4u8A==}

  '@fast-csv/parse@4.3.6':
    resolution: {integrity: sha512-uRsLYksqpbDmWaSmzvJcuApSEe38+6NQZBUsuAyMZKqHxH0g1wcJgsKUvN3WC8tewaqFjBMMGrkHmC+T7k8LvA==}

  '@icons/material@0.2.4':
    resolution: {integrity: sha512-QPcGmICAPbGLGb6F/yNf/KzKqvFx8z5qx3D1yFqVAjoFmXK35EgyW+cJ57Te3CNsmzblwtzakLGFqHPqrfb4Tw==}
    peerDependencies:
      react: '*'

  '@jridgewell/sourcemap-codec@1.5.5':
    resolution: {integrity: sha512-cYQ9310grqxueWbl+WuIUIaiUaDcj7WOq5fVhEljNVgRfOUhY9fy2zTvfoqWsnebh8Sl70VScFbICvJnLKB0Og==}

  '@mapbox/node-pre-gyp@1.0.11':
    resolution: {integrity: sha512-Yhlar6v9WQgUp/He7BdgzOz8lqMQ8sU+jkCq7Wx8Myc5YFJLbEe7lgui/V7G1qB1DJykHSGwreceSaD60Y0PUQ==}
    hasBin: true

  '@popperjs/core@2.11.8':
    resolution: {integrity: sha512-P1st0aksCrn9sGZhp8GMYwBnQsbvAWsZAX44oXNNvLHGqAOcoVxmjZiohstwQ7SqKnbR47akdNi+uleWD8+g6A==}

  '@rc-component/mini-decimal@1.1.0':
    resolution: {integrity: sha512-jS4E7T9Li2GuYwI6PyiVXmxTiM6b07rlD9Ge8uGZSCz3WlzcG5ZK7g5bbuKNeZ9pgUuPK/5guV781ujdVpm4HQ==}
    engines: {node: '>=8.x'}

  '@rc-component/portal@1.1.2':
    resolution: {integrity: sha512-6f813C0IsasTZms08kfA8kPAGxbbkYToa8ALaiDIGGECU4i9hj8Plgbx0sNJDrey3EtHO30hmdaxtT0138xZcg==}
    engines: {node: '>=8.x'}
    peerDependencies:
      react: '>=16.9.0'
      react-dom: '>=16.9.0'

  '@rc-component/trigger@2.3.0':
    resolution: {integrity: sha512-iwaxZyzOuK0D7lS+0AQEtW52zUWxoGqTGkke3dRyb8pYiShmRpCjB/8TzPI4R6YySCH7Vm9BZj/31VPiiQTLBg==}
    engines: {node: '>=8.x'}
    peerDependencies:
      react: '>=16.9.0'
      react-dom: '>=16.9.0'

  '@restart/hooks@0.3.27':
    resolution: {integrity: sha512-s984xV/EapUIfkjlf8wz9weP2O9TNKR96C68FfMEy2bE69+H4cNv3RD4Mf97lW7Htt7PjZrYTjSC8f3SB9VCXw==}
    peerDependencies:
      react: '>=16.8.0'

  '@rolldown/pluginutils@1.0.0-beta.29':
    resolution: {integrity: sha512-NIJgOsMjbxAXvoGq/X0gD7VPMQ8j9g0BiDaNjVNVjvl+iKXxL3Jre0v31RmBYeLEmkbj2s02v8vFTbUXi5XS2Q==}

  '@rollup/rollup-android-arm-eabi@4.50.0':
    resolution: {integrity: sha512-lVgpeQyy4fWN5QYebtW4buT/4kn4p4IJ+kDNB4uYNT5b8c8DLJDg6titg20NIg7E8RWwdWZORW6vUFfrLyG3KQ==}
    cpu: [arm]
    os: [android]

  '@rollup/rollup-android-arm64@4.50.0':
    resolution: {integrity: sha512-2O73dR4Dc9bp+wSYhviP6sDziurB5/HCym7xILKifWdE9UsOe2FtNcM+I4xZjKrfLJnq5UR8k9riB87gauiQtw==}
    cpu: [arm64]
    os: [android]

  '@rollup/rollup-darwin-arm64@4.50.0':
    resolution: {integrity: sha512-vwSXQN8T4sKf1RHr1F0s98Pf8UPz7pS6P3LG9NSmuw0TVh7EmaE+5Ny7hJOZ0M2yuTctEsHHRTMi2wuHkdS6Hg==}
    cpu: [arm64]
    os: [darwin]

  '@rollup/rollup-darwin-x64@4.50.0':
    resolution: {integrity: sha512-cQp/WG8HE7BCGyFVuzUg0FNmupxC+EPZEwWu2FCGGw5WDT1o2/YlENbm5e9SMvfDFR6FRhVCBePLqj0o8MN7Vw==}
    cpu: [x64]
    os: [darwin]

  '@rollup/rollup-freebsd-arm64@4.50.0':
    resolution: {integrity: sha512-UR1uTJFU/p801DvvBbtDD7z9mQL8J80xB0bR7DqW7UGQHRm/OaKzp4is7sQSdbt2pjjSS72eAtRh43hNduTnnQ==}
    cpu: [arm64]
    os: [freebsd]

  '@rollup/rollup-freebsd-x64@4.50.0':
    resolution: {integrity: sha512-G/DKyS6PK0dD0+VEzH/6n/hWDNPDZSMBmqsElWnCRGrYOb2jC0VSupp7UAHHQ4+QILwkxSMaYIbQ72dktp8pKA==}
    cpu: [x64]
    os: [freebsd]

  '@rollup/rollup-linux-arm-gnueabihf@4.50.0':
    resolution: {integrity: sha512-u72Mzc6jyJwKjJbZZcIYmd9bumJu7KNmHYdue43vT1rXPm2rITwmPWF0mmPzLm9/vJWxIRbao/jrQmxTO0Sm9w==}
    cpu: [arm]
    os: [linux]

  '@rollup/rollup-linux-arm-musleabihf@4.50.0':
    resolution: {integrity: sha512-S4UefYdV0tnynDJV1mdkNawp0E5Qm2MtSs330IyHgaccOFrwqsvgigUD29uT+B/70PDY1eQ3t40+xf6wIvXJyg==}
    cpu: [arm]
    os: [linux]

  '@rollup/rollup-linux-arm64-gnu@4.50.0':
    resolution: {integrity: sha512-1EhkSvUQXJsIhk4msxP5nNAUWoB4MFDHhtc4gAYvnqoHlaL9V3F37pNHabndawsfy/Tp7BPiy/aSa6XBYbaD1g==}
    cpu: [arm64]
    os: [linux]

  '@rollup/rollup-linux-arm64-musl@4.50.0':
    resolution: {integrity: sha512-EtBDIZuDtVg75xIPIK1l5vCXNNCIRM0OBPUG+tbApDuJAy9mKago6QxX+tfMzbCI6tXEhMuZuN1+CU8iDW+0UQ==}
    cpu: [arm64]
    os: [linux]

  '@rollup/rollup-linux-loongarch64-gnu@4.50.0':
    resolution: {integrity: sha512-BGYSwJdMP0hT5CCmljuSNx7+k+0upweM2M4YGfFBjnFSZMHOLYR0gEEj/dxyYJ6Zc6AiSeaBY8dWOa11GF/ppQ==}
    cpu: [loong64]
    os: [linux]

  '@rollup/rollup-linux-ppc64-gnu@4.50.0':
    resolution: {integrity: sha512-I1gSMzkVe1KzAxKAroCJL30hA4DqSi+wGc5gviD0y3IL/VkvcnAqwBf4RHXHyvH66YVHxpKO8ojrgc4SrWAnLg==}
    cpu: [ppc64]
    os: [linux]

  '@rollup/rollup-linux-riscv64-gnu@4.50.0':
    resolution: {integrity: sha512-bSbWlY3jZo7molh4tc5dKfeSxkqnf48UsLqYbUhnkdnfgZjgufLS/NTA8PcP/dnvct5CCdNkABJ56CbclMRYCA==}
    cpu: [riscv64]
    os: [linux]

  '@rollup/rollup-linux-riscv64-musl@4.50.0':
    resolution: {integrity: sha512-LSXSGumSURzEQLT2e4sFqFOv3LWZsEF8FK7AAv9zHZNDdMnUPYH3t8ZlaeYYZyTXnsob3htwTKeWtBIkPV27iQ==}
    cpu: [riscv64]
    os: [linux]

  '@rollup/rollup-linux-s390x-gnu@4.50.0':
    resolution: {integrity: sha512-CxRKyakfDrsLXiCyucVfVWVoaPA4oFSpPpDwlMcDFQvrv3XY6KEzMtMZrA+e/goC8xxp2WSOxHQubP8fPmmjOQ==}
    cpu: [s390x]
    os: [linux]

  '@rollup/rollup-linux-x64-gnu@4.50.0':
    resolution: {integrity: sha512-8PrJJA7/VU8ToHVEPu14FzuSAqVKyo5gg/J8xUerMbyNkWkO9j2ExBho/68RnJsMGNJq4zH114iAttgm7BZVkA==}
    cpu: [x64]
    os: [linux]

  '@rollup/rollup-linux-x64-musl@4.50.0':
    resolution: {integrity: sha512-SkE6YQp+CzpyOrbw7Oc4MgXFvTw2UIBElvAvLCo230pyxOLmYwRPwZ/L5lBe/VW/qT1ZgND9wJfOsdy0XptRvw==}
    cpu: [x64]
    os: [linux]

  '@rollup/rollup-openharmony-arm64@4.50.0':
    resolution: {integrity: sha512-PZkNLPfvXeIOgJWA804zjSFH7fARBBCpCXxgkGDRjjAhRLOR8o0IGS01ykh5GYfod4c2yiiREuDM8iZ+pVsT+Q==}
    cpu: [arm64]
    os: [openharmony]

  '@rollup/rollup-win32-arm64-msvc@4.50.0':
    resolution: {integrity: sha512-q7cIIdFvWQoaCbLDUyUc8YfR3Jh2xx3unO8Dn6/TTogKjfwrax9SyfmGGK6cQhKtjePI7jRfd7iRYcxYs93esg==}
    cpu: [arm64]
    os: [win32]

  '@rollup/rollup-win32-ia32-msvc@4.50.0':
    resolution: {integrity: sha512-XzNOVg/YnDOmFdDKcxxK410PrcbcqZkBmz+0FicpW5jtjKQxcW1BZJEQOF0NJa6JO7CZhett8GEtRN/wYLYJuw==}
    cpu: [ia32]
    os: [win32]

  '@rollup/rollup-win32-x64-msvc@4.50.0':
    resolution: {integrity: sha512-xMmiWRR8sp72Zqwjgtf3QbZfF1wdh8X2ABu3EaozvZcyHJeU0r+XAnXdKgs4cCAp6ORoYoCygipYP1mjmbjrsg==}
    cpu: [x64]
    os: [win32]

  '@types/estree@1.0.8':
    resolution: {integrity: sha512-dWHzHa2WqEXI/O1E9OjrocMTKJl2mSrEolh1Iomrv6U+JuNwaHXsXx9bLu5gG7BUWFIN0skIQJQ/L1rIex4X6w==}

  '@types/node@14.18.63':
    resolution: {integrity: sha512-fAtCfv4jJg+ExtXhvCkCqUKZ+4ok/JQk01qDKhL5BDDoS3AxKXhV5/MAVUZyQnSEd2GT92fkgZl0pz0Q0AzcIQ==}

  '@types/react@19.1.12':
    resolution: {integrity: sha512-cMoR+FoAf/Jyq6+Df2/Z41jISvGZZ2eTlnsaJRptmZ76Caldwy1odD4xTr/gNV9VLj0AWgg/nmkevIyUfIIq5w==}

  '@types/warning@3.0.3':
    resolution: {integrity: sha512-D1XC7WK8K+zZEveUPY+cf4+kgauk8N4eHr/XIHXGlGYkHLud6hK9lYfZk1ry1TNh798cZUCgb6MqGEG8DkJt6Q==}

  '@vitejs/plugin-vue@6.0.1':
    resolution: {integrity: sha512-+MaE752hU0wfPFJEUAIxqw18+20euHHdxVtMvbFcOEpjEyfqXH/5DCoTHiVJ0J29EhTJdoTkjEv5YBKU9dnoTw==}
    engines: {node: ^20.19.0 || >=22.12.0}
    peerDependencies:
      vite: ^5.0.0 || ^6.0.0 || ^7.0.0
      vue: ^3.2.25

  '@vue/compiler-core@3.5.21':
    resolution: {integrity: sha512-8i+LZ0vf6ZgII5Z9XmUvrCyEzocvWT+TeR2VBUVlzIH6Tyv57E20mPZ1bCS+tbejgUgmjrEh7q/0F0bibskAmw==}

  '@vue/compiler-dom@3.5.21':
    resolution: {integrity: sha512-jNtbu/u97wiyEBJlJ9kmdw7tAr5Vy0Aj5CgQmo+6pxWNQhXZDPsRr1UWPN4v3Zf82s2H3kF51IbzZ4jMWAgPlQ==}

  '@vue/compiler-sfc@3.5.21':
    resolution: {integrity: sha512-SXlyk6I5eUGBd2v8Ie7tF6ADHE9kCR6mBEuPyH1nUZ0h6Xx6nZI29i12sJKQmzbDyr2tUHMhhTt51Z6blbkTTQ==}

  '@vue/compiler-ssr@3.5.21':
    resolution: {integrity: sha512-vKQ5olH5edFZdf5ZrlEgSO1j1DMA4u23TVK5XR1uMhvwnYvVdDF0nHXJUblL/GvzlShQbjhZZ2uvYmDlAbgo9w==}

  '@vue/devtools-api@6.6.4':
    resolution: {integrity: sha512-sGhTPMuXqZ1rVOk32RylztWkfXTRhuS7vgAKv0zjqk8gbsHkJ7xfFf+jbySxt7tWObEJwyKaHMikV/WGDiQm8g==}

  '@vue/reactivity@3.5.21':
    resolution: {integrity: sha512-3ah7sa+Cwr9iiYEERt9JfZKPw4A2UlbY8RbbnH2mGCE8NwHkhmlZt2VsH0oDA3P08X3jJd29ohBDtX+TbD9AsA==}

  '@vue/runtime-core@3.5.21':
    resolution: {integrity: sha512-+DplQlRS4MXfIf9gfD1BOJpk5RSyGgGXD/R+cumhe8jdjUcq/qlxDawQlSI8hCKupBlvM+3eS1se5xW+SuNAwA==}

  '@vue/runtime-dom@3.5.21':
    resolution: {integrity: sha512-3M2DZsOFwM5qI15wrMmNF5RJe1+ARijt2HM3TbzBbPSuBHOQpoidE+Pa+XEaVN+czbHf81ETRoG1ltztP2em8w==}

  '@vue/server-renderer@3.5.21':
    resolution: {integrity: sha512-qr8AqgD3DJPJcGvLcJKQo2tAc8OnXRcfxhOJCPF+fcfn5bBGz7VCcO7t+qETOPxpWK1mgysXvVT/j+xWaHeMWA==}
    peerDependencies:
      vue: 3.5.21

  '@vue/shared@3.5.21':
    resolution: {integrity: sha512-+2k1EQpnYuVuu3N7atWyG3/xoFWIVJZq4Mz8XNOdScFI0etES75fbny/oU4lKWk/577P1zmg0ioYvpGEDZ3DLw==}

  '@webcomponents/webcomponentsjs@2.8.0':
    resolution: {integrity: sha512-loGD63sacRzOzSJgQnB9ZAhaQGkN7wl2Zuw7tsphI5Isa0irijrRo6EnJii/GgjGefIFO8AIO7UivzRhFaEk9w==}

  abbrev@1.1.1:
    resolution: {integrity: sha512-nne9/IiQ/hzIhY6pdDnbBtz7DjPTKrY00P/zvPSm5pOFkl6xuGrGnXn/VtTNNfNtAfZ9/1RtehkszU9qcTii0Q==}

  adler-32@1.3.1:
    resolution: {integrity: sha512-ynZ4w/nUUv5rrsR8UUGoe1VC9hZj6V5hU9Qw1HlMDJGEJw5S7TfTErWTjMys6M7vr0YWcPqs3qAr4ss0nDfP+A==}
    engines: {node: '>=0.8'}

  agent-base@6.0.2:
    resolution: {integrity: sha512-RZNwNclF7+MS/8bDg70amg32dyeZGZxiDuQmZxKLAlQjr3jGyLx+4Kkk58UO7D2QdgFIQCovuSuZESne6RG6XQ==}
    engines: {node: '>= 6.0.0'}

  amis-core@6.13.0:
    resolution: {integrity: sha512-RLyG3azQXlihxLqeRwf+hiKMDYyg3C3P7Kh/oLgMg0c4OKl0dgWfyhE8tUok+et5fc7phoMb6FuKeh+J0MOseg==}
    peerDependencies:
      amis-formula: '*'
      react: '>=16.8.6'
      react-dom: '>=16.8.6'
      react-is: '>=16.8.6'

  amis-editor-core@6.13.0:
    resolution: {integrity: sha512-5ZBOmThMZ6ccyY4gyhq7CYnXw5Gn+Nm198h/Owc8NTN8UgxK/Rfponea2BUp8luZHvrib4E+oYhu+9hy1KxoOw==}
    peerDependencies:
      amis: '*'
      amis-core: '*'
      amis-formula: '*'
      amis-theme-editor-helper: '*'
      amis-ui: '*'
      i18n-runtime: '*'
      react: '>=16.8.6'
      react-dom: '>=16.8.6'

  amis-editor@6.13.0:
    resolution: {integrity: sha512-pUu1MkvrZHTK7KlMECg8VyBMoKpejZBsbjuK29Lpdd71FNWnoQhf2H7TGv0/YZgrVIq6BuDZ9i7E/WDlMV4NpQ==}
    peerDependencies:
      amis: '*'
      amis-core: '*'
      amis-formula: '*'
      amis-theme-editor-helper: '*'
      amis-ui: '*'
      i18n-runtime: '*'
      react: '>=16.8.6'
      react-dom: '>=16.8.6'

  amis-formula@6.13.0:
    resolution: {integrity: sha512-KoapL+V94Qy5olTEJpSffyngyNBhW9jJkjZogJXbHw7yVdQY2EmJ6mfkFdPsHri/DmNLlAfE2xJtT2kLFnvrnQ==}

  amis-postcss@1.0.0:
    resolution: {integrity: sha512-OeKbbTvsaIcOq2SZ1VV1GyshhF2fFbFXXc8IQ3ZTIu0uTtJBGFVYZ6sJ40nOznWeptEdfYpVdB/JEtBlXlSWag==}

  amis-theme-editor-helper@2.0.27:
    resolution: {integrity: sha512-7DPAymJyUcaxsxR7XvNxeXD3n6lYJYI+9wUmGvVtV7I85K8FbHlrUEZlHURVG9uI4YkktqUcToRZMcP4gqBOQA==}
    peerDependencies:
      amis: '*'
      amis-core: '*'
      amis-ui: '*'
      i18n-runtime: '*'
      react: '>=16.8.6'
      react-dom: '>=16.8.6'

  amis-ui@6.13.0:
    resolution: {integrity: sha512-6BLZE6c+V7m7oTyG5rkDpzX9KIxkDT7oh7Grl5gHkSTrUJVykPnydCAVanb/kzabZhdBjL8gdT9Lfz8l36tpxA==}
    peerDependencies:
      amis-core: '*'
      amis-formula: '*'
      react: '>=16.8.6'
      react-dom: '>=16.8.6'

  amis@6.13.0:
    resolution: {integrity: sha512-KRE5e6dfnaVyoBagrgSl8lcW7OsZcTAzVx0EVCInpayMsuiorO44LABMRhTvi9IBk9DhjzgczBKj6frnQ4pJsQ==}
    peerDependencies:
      amis-core: '*'
      amis-ui: '*'
      office-viewer: '*'
      react: '>=16.8.6'
      react-dom: '>=16.8.6'

  animate.css@4.1.1:
    resolution: {integrity: sha512-+mRmCTv6SbCmtYJCN4faJMNFVNN5EuCTTprDTAo7YzIGji2KADmakjVA3+8mVDkZ2Bf09vayB35lSQIex2+QaQ==}

  ansi-regex@5.0.1:
    resolution: {integrity: sha512-quJQXlTSUGL2LH9SUXo8VwsY4soanhgo6LNSm84E1LBcE8s3O0wpdiRzyR9z/ZZJMlMWv37qOOb9pdJlMUEKFQ==}
    engines: {node: '>=8'}

  aproba@2.1.0:
    resolution: {integrity: sha512-tLIEcj5GuR2RSTnxNKdkK0dJ/GrC7P38sUkiDmDuHfsHmbagTFAxDVIBltoklXEVIQ/f14IL8IMJ5pn9Hez1Ew==}

  archiver-utils@2.1.0:
    resolution: {integrity: sha512-bEL/yUb/fNNiNTuUz979Z0Yg5L+LzLxGJz8x79lYmR54fmTIb6ob/hNQgkQnIUDWIFjZVQwl9Xs356I6BAMHfw==}
    engines: {node: '>= 6'}

  archiver-utils@3.0.4:
    resolution: {integrity: sha512-KVgf4XQVrTjhyWmx6cte4RxonPLR9onExufI1jhvw/MQ4BB6IsZD5gT8Lq+u/+pRkWna/6JoHpiQioaqFP5Rzw==}
    engines: {node: '>= 10'}

  archiver@5.3.2:
    resolution: {integrity: sha512-+25nxyyznAXF7Nef3y0EbBeqmGZgeN/BxHX29Rs39djAfaFalmQ89SE6CWyDCHzGL0yt/ycBtNOmGTW0FyGWNw==}
    engines: {node: '>= 10'}

  are-we-there-yet@2.0.0:
    resolution: {integrity: sha512-Ci/qENmwHnsYo9xKIcUJN5LeDKdJ6R1Z1j9V/J5wyq8nh/mYPEpIKJbBZXtZjG04HiK7zV/p6Vs9952MrMeUIw==}
    engines: {node: '>=10'}
    deprecated: This package is no longer supported.

  argparse@2.0.1:
    resolution: {integrity: sha512-8+9WqebbFzpX9OR+Wa6O29asIogeRMzcGtAINdpMHHyAg10f05aSFVBbcEqGf/PXw1EjAZ+q2/bEBg3DvurK3Q==}

  asap@2.0.6:
    resolution: {integrity: sha512-BSHWgDSAiKs50o2Re8ppvp3seVHXSRM44cdSsT9FfNEUUZLOGWVCsiWaRPWM1Znn+mqZ1OfVZ3z3DWEzSp7hRA==}

  async@3.2.6:
    resolution: {integrity: sha512-htCUDlxyyCLMgaM3xXg0C0LW2xqfuQ6p05pCEIsXuyQ+a1koYKTuBMzRNwmybfLgvJDMd0r1LTn4+E0Ti6C2AA==}

  attr-accept@2.2.2:
    resolution: {integrity: sha512-7prDjvt9HmqiZ0cl5CRjtS84sEyhsHP2coDkaZKRKVfCDo9s7iw7ChVmar78Gu9pC4SoR/28wFu/G5JJhTnqEg==}
    engines: {node: '>=4'}

  axios@0.21.1:
    resolution: {integrity: sha512-dKQiRHxGD9PPRIUNIWvZhPTPpl1rf/OxTYKsqKUDjBwYylTvV7SjSHJb9ratfyzM6wCdLCOYLzs73qpg5c4iGA==}

  balanced-match@1.0.2:
    resolution: {integrity: sha512-3oSeUO0TMV67hN1AmbXsK4yaqU7tjiHlbxRDZOpH0KW9+CeX4bRAaX0Anxt0tx2MrpRpWwQaPwIlISEJhYU5Pw==}

  base16@1.0.0:
    resolution: {integrity: sha512-pNdYkNPiJUnEhnfXV56+sQy8+AaPcG3POZAUnwr4EeqCUZFz4u2PePbo3e5Gj4ziYPCWGUZT9RHisvJKnwFuBQ==}

  base64-js@1.5.1:
    resolution: {integrity: sha512-AKpaYlHn8t4SVbOHCy+b5+KKgvR4vrsD8vbvrbiQJps7fKDTkjkDry6ji0rUJjC0kzbNePLwzxq8iypo41qeWA==}

  big-integer@1.6.52:
    resolution: {integrity: sha512-QxD8cf2eVqJOOz63z6JIN9BzvVs/dlySa5HGSBH5xtR8dPteIRQnBxxKqkNTiT6jbDTF6jAfrd4oMcND9RGbQg==}
    engines: {node: '>=0.6'}

  binary@0.3.0:
    resolution: {integrity: sha512-D4H1y5KYwpJgK8wk1Cue5LLPgmwHKYSChkbspQg5JtVuR5ulGckxfR62H3AE9UDkdMC8yyXlqYihuz3Aqg2XZg==}

  bl@4.1.0:
    resolution: {integrity: sha512-1W07cM9gS6DcLperZfFSj+bWLtaPGSOHWhPiGzXmvVJbRLdG82sH/Kn8EtW1VqWVA54AKf2h5k5BbnIbwF3h6w==}

  bluebird@3.4.7:
    resolution: {integrity: sha512-iD3898SR7sWVRHbiQv+sHUtHnMvC1o3nW5rAcqnq3uOn07DSAppZYUkIGslDz6gXC7HfunPe7YVBgoEJASPcHA==}

  blueimp-canvastoblob@2.1.0:
    resolution: {integrity: sha512-XExr0EIfB/ugmmrht7mcq4h5UXCaaC37jFF3m0h0WCBayku3Q9Hm227tCCDuGvD74/HIKJJ6xEoDJVHjdMcQ0g==}

  brace-expansion@1.1.12:
    resolution: {integrity: sha512-9T9UjW3r0UW5c1Q7GTwllptXwhvYmEzFhzMfZ9H7FQWt+uZePjZPjBP/W1ZEyZ1twGWom5/56TF4lPcqjnDHcg==}

  brace-expansion@2.0.2:
    resolution: {integrity: sha512-Jt0vHyM+jmUBqojB7E1NIYadt0vI0Qxjxd2TErW94wDz+E2LAm5vKMXXwg6ZZBTHPuUlDgQHKXvjGBdfcF1ZDQ==}

  buffer-crc32@0.2.13:
    resolution: {integrity: sha512-VO9Ht/+p3SN7SKWqcrgEzjGbRSJYTx+Q1pTQC0wrWqHx0vpJraQ6GtHx8tvcg1rlK1byhU5gccxgOgj7B0TDkQ==}

  buffer-indexof-polyfill@1.0.2:
    resolution: {integrity: sha512-I7wzHwA3t1/lwXQh+A5PbNvJxgfo5r3xulgpYDB5zckTu/Z9oUK9biouBKQUjEqzaz3HnAT6TYoovmE+GqSf7A==}
    engines: {node: '>=0.10'}

  buffer@5.7.1:
    resolution: {integrity: sha512-EHcyIPBQ4BSGlvjB16k5KgAJ27CIsHY/2JBmCRReo48y9rQ3MaUzWX3KVlBa4U7MyX02HdVj0K7C3WaB3ju7FQ==}

  buffers@0.1.1:
    resolution: {integrity: sha512-9q/rDEGSb/Qsvv2qvzIzdluL5k7AaJOTrw23z9reQthrbF7is4CtlT0DXyO1oei2DCp4uojjzQ7igaSHp1kAEQ==}
    engines: {node: '>=0.2.0'}

  canvas@2.11.2:
    resolution: {integrity: sha512-ItanGBMrmRV7Py2Z+Xhs7cT+FNt5K0vPL4p9EZ/UX/Mu7hFbkxSjKF2KVtPwX7UYWp7dRKnrTvReflgrItJbdw==}
    engines: {node: '>=6'}

  cfb@1.2.2:
    resolution: {integrity: sha512-KfdUZsSOw19/ObEWasvBP/Ac4reZvAGauZhs6S/gqNhXhI7cKwvlH7ulj+dOEYnca4bm4SGo8C1bTAQvnTjgQA==}
    engines: {node: '>=0.8'}

  chainsaw@0.1.0:
    resolution: {integrity: sha512-75kWfWt6MEKNC8xYXIdRpDehRYY/tNSgwKaJq+dbbDcxORuVrrQ+SEHoWsniVn9XPYfP4gmdWIeDk/4YNp1rNQ==}

  chownr@2.0.0:
    resolution: {integrity: sha512-bIomtDF5KGpdogkLd9VspvFzk9KfpyyGlS8YFVZl7TGPBHL5snIOnxeshwVgPteQ9b4Eydl+pVbIyE1DcvCWgQ==}
    engines: {node: '>=10'}

  classnames@2.3.2:
    resolution: {integrity: sha512-CSbhY4cFEJRe6/GQzIk5qXZ4Jeg5pcsP7b5peFSDpffpe1cqjASH/n9UTjBwOp6XpMSTwQ8Za2K5V02ueA7Tmw==}

  clsx@2.1.1:
    resolution: {integrity: sha512-eYm0QWBtUrBWZWG0d386OGAw16Z995PiOVo2B7bjWSbHedGl5e0ZWaq65kOGgUSNesEIDkB9ISbTg/JK9dhCZA==}
    engines: {node: '>=6'}

  codemirror@5.65.20:
    resolution: {integrity: sha512-i5dLDDxwkFCbhjvL2pNjShsojoL3XHyDwsGv1jqETUoW+lzpBKKqNTUWgQwVAOa0tUm4BwekT455ujafi8payA==}

  codepage@1.15.0:
    resolution: {integrity: sha512-3g6NUTPd/YtuuGrhMnOMRjFc+LJw/bnMp3+0r/Wcz3IXUuCosKRJvMphm5+Q+bvTVGcJJuRvVLuYba+WojaFaA==}
    engines: {node: '>=0.8'}

  color-support@1.1.3:
    resolution: {integrity: sha512-qiBjkpbMLO/HL68y+lh4q0/O1MZFj2RX6X/KmMa3+gJD3z+WwI1ZzDHysvqHGS3mP6mznPckpXmw1nI9cJjyRg==}
    hasBin: true

  compress-commons@4.1.2:
    resolution: {integrity: sha512-D3uMHtGc/fcO1Gt1/L7i1e33VOvD4A9hfQLP+6ewd+BvG/gQ84Yh4oftEhAdjSMgBgwGL+jsppT7JYNpo6MHHg==}
    engines: {node: '>= 10'}

  compute-scroll-into-view@1.0.20:
    resolution: {integrity: sha512-UCB0ioiyj8CRjtrvaceBLqqhZCVP+1B8+NWQhmdsm0VXOJtobBCf1dBQmebCCo34qZmUwZfIH2MZLqNHazrfjg==}

  concat-map@0.0.1:
    resolution: {integrity: sha512-/Srv4dswyQNBfohGpz9o6Yb3Gz3SrUDqBH5rTuhGR7ahtlbYKnVxw2bCFMRljaA7EXHaXZ8wsHdodFvbkhKmqg==}

  console-control-strings@1.1.0:
    resolution: {integrity: sha512-ty/fTekppD2fIwRvnZAVdeOiGd1c7YXEixbgJTNzqcxJWKQnjJ/V1bNEEE6hygpM3WjwHFUVK6HTjWSzV4a8sQ==}

  core-util-is@1.0.3:
    resolution: {integrity: sha512-ZQBvi1DcpJ4GDqanjucZ2Hj3wEO5pZDS89BWbkcrvdxksJorwUDDZamX9ldFkp9aw2lmBDLgkObEA4DWNJ9FYQ==}

  crc-32@1.2.2:
    resolution: {integrity: sha512-ROmzCKrTnOwybPcJApAA6WBWij23HVfGVNKqqrZpuyZOHqK2CwHSvpGuyt/UNNvaIjEd8X5IFGp4Mh+Ie1IHJQ==}
    engines: {node: '>=0.8'}
    hasBin: true

  crc32-stream@4.0.3:
    resolution: {integrity: sha512-NT7w2JVU7DFroFdYkeq8cywxrgjPHWkdX1wjpRQXPX5Asews3tA+Ght6lddQO5Mkumffp3X7GEqku3epj2toIw==}
    engines: {node: '>= 10'}

  cropperjs@1.6.2:
    resolution: {integrity: sha512-nhymn9GdnV3CqiEHJVai54TULFAE3VshJTXSqSJKa8yXAKyBKDWdhHarnlIPrshJ0WMFTGuFvG02YjLXfPiuOA==}

  cross-env@7.0.3:
    resolution: {integrity: sha512-+/HKd6EgcQCJGh2PSjZuUitQBQynKor4wrFbRg4DtAgS1aWO+gU52xpH7M9ScGgXSYmAVS9bIJ8EzuaGw0oNAw==}
    engines: {node: '>=10.14', npm: '>=6', yarn: '>=1'}
    hasBin: true

  cross-fetch@3.2.0:
    resolution: {integrity: sha512-Q+xVJLoGOeIMXZmbUK4HYk+69cQH6LudR0Vu/pRm2YlU/hDV9CiS0gKUMaWY5f2NeUH9C1nV3bsTlCo0FsTV1Q==}

  cross-spawn@7.0.6:
    resolution: {integrity: sha512-uV2QOWP2nWzsy2aMp8aRibhi9dlzF5Hgh5SHaB9OiTGEyDTiJJyx0uy51QXdyWbtAHNua4XJzUKca3OzKUd3vA==}
    engines: {node: '>= 8'}

  csstype@3.1.3:
    resolution: {integrity: sha512-M1uQkMl8rQK/szD0LNhtqxIPLpimGm8sOBwU7lLnCpSbTyY3yeU1Vc7l4KT5zT4s/yOxHH5O7tIuuLOCnLADRw==}

  dayjs@1.11.18:
    resolution: {integrity: sha512-zFBQ7WFRvVRhKcWoUh+ZA1g2HVgUbsZm9sbddh8EC5iv93sui8DVVz1Npvz+r6meo9VKfa8NyLWBsQK1VvIKPA==}

  debug@4.4.1:
    resolution: {integrity: sha512-KcKCqiftBJcZr++7ykoDIEwSa3XWowTfNPo92BYxjXiyYEVrUQh2aLyhxBCwww+heortUFxEJYcRzosstTEBYQ==}
    engines: {node: '>=6.0'}
    peerDependencies:
      supports-color: '*'
    peerDependenciesMeta:
      supports-color:
        optional: true

  decompress-response@4.2.1:
    resolution: {integrity: sha512-jOSne2qbyE+/r8G1VU+G/82LBs2Fs4LAsTiLSHOCOMZQl2OKZ6i8i4IyHemTe+/yIXOtTcRQMzPcgyhoFlqPkw==}
    engines: {node: '>=8'}

  deep-diff@1.0.2:
    resolution: {integrity: sha512-aWS3UIVH+NPGCD1kki+DCU9Dua032iSsO43LqQpcs4R3+dVv7tX0qBGjiVHJHjplsoUM2XRO/KB92glqc68awg==}

  delegates@1.0.0:
    resolution: {integrity: sha512-bd2L678uiWATM6m5Z1VzNCErI3jiGzt6HGY8OVICs40JQq/HALfbyNJmp0UDakEY4pMMaN0Ly5om/B1VI/+xfQ==}

  dequal@2.0.3:
    resolution: {integrity: sha512-0je+qPKHEMohvfRTCEo3CrPG6cAzAYgmzKyxRiYSSDkS6eGJdyVJm7WaYA5ECaAD9wLB2T4EEeymA5aFVcYXCA==}
    engines: {node: '>=6'}

  detect-libc@2.0.4:
    resolution: {integrity: sha512-3UDv+G9CsCKO1WKMGw9fwq/SWJYbI0c5Y7LU1AXYoDdbhE2AHQ6N6Nb34sG8Fj7T5APy8qXDCKuuIHd1BR0tVA==}
    engines: {node: '>=8'}

  dom-helpers@5.2.1:
    resolution: {integrity: sha512-nRCa7CK3VTrM2NmGkIy4cbK7IZlgBE/PYMn55rrXefr5xXDP0LdtfPnblFDoVdcAfslJ7or6iqAUnx0CCGIWQA==}

  downshift@6.1.12:
    resolution: {integrity: sha512-7XB/iaSJVS4T8wGFT3WRXmSF1UlBHAA40DshZtkrIscIN+VC+Lh363skLxFTvJwtNgHxAMDGEHT4xsyQFWL+UA==}
    peerDependencies:
      react: '>=16.12.0'

  duplexer2@0.1.4:
    resolution: {integrity: sha512-asLFVfWWtJ90ZyOUHMqk7/S2w2guQKxUI2itj3d92ADHhxUSbCMGi1f1cBcJ7xM1To+pE/Khbwo1yuNbMEPKeA==}

  echarts-stat@1.2.0:
    resolution: {integrity: sha512-zLd7Kgs+tuTSeaK0VQEMNmnMivEkhvHIk1gpBtLzpRerfcIQ+Bd5XudOMmtwpaTc1WDZbA7d1V//iiBccR46Qg==}

  echarts-wordcloud@2.1.0:
    resolution: {integrity: sha512-Kt1JmbcROgb+3IMI48KZECK2AP5lG6bSsOEs+AsuwaWJxQom31RTNd6NFYI01E/YaI1PFZeueaupjlmzSQasjQ==}
    peerDependencies:
      echarts: ^5.0.1

  echarts@5.5.1:
    resolution: {integrity: sha512-Fce8upazaAXUVUVsjgV6mBnGuqgO+JNDlcgF79Dksy4+wgGpQB2lmYoO4TSweFg/mZITdpGHomw/cNBJZj1icA==}

  emoji-regex@8.0.0:
    resolution: {integrity: sha512-MSjYzcWNOA0ewAHpz0MxpYFvwg6yjy1NG3xteoqz644VCo/RPgnr1/GGt+ic3iJTzQ8Eu3TdM14SawnVUmGE6A==}

  end-of-stream@1.4.5:
    resolution: {integrity: sha512-ooEGc6HP26xXq/N+GCGOT0JKCLDGrq2bQUZrQ7gyrJiZANJ/8YDTxTpQBXGMn+WbIQXNVpyWymm7KYVICQnyOg==}

  entities@2.1.0:
    resolution: {integrity: sha512-hCx1oky9PFrJ611mf0ifBLBRW8lUUVRlFolb5gWRfIELabBlbp9xZvrqZLZAs+NxFnbfQoeGd8wDkygjg7U85w==}

  entities@4.5.0:
    resolution: {integrity: sha512-V0hjH4dGPh9Ao5p0MoRY6BVqtwCjhz6vI5LT8AJ55H+4g9/4vbHx1I54fS0XuclLhDHArPQCiMjDxjaL8fPxhw==}
    engines: {node: '>=0.12'}

  es6-promise@4.2.8:
    resolution: {integrity: sha512-HJDGx5daxeIvxdBxvG2cb9g4tEvwIk3i8+nhX0yGrYmZUzbkdg8QbDevheDB8gd0//uPj4c1EQua8Q+MViT0/w==}

  esbuild@0.25.9:
    resolution: {integrity: sha512-CRbODhYyQx3qp7ZEwzxOk4JBqmD/seJrzPa/cGjY1VtIn5E09Oi9/dB4JwctnfZ8Q8iT7rioVv5k/FNT/uf54g==}
    engines: {node: '>=18'}
    hasBin: true

  estree-walker@2.0.2:
    resolution: {integrity: sha512-Rfkk/Mp/DL7JVje3u18FxFujQlTNR2q6QfMSMB7AvCBx91NGj/ba3kCfza0f6dVDbw7YlRf/nDrn7pQrCCyQ/w==}

  exceljs@4.4.0:
    resolution: {integrity: sha512-XctvKaEMaj1Ii9oDOqbW/6e1gXknSY4g/aLCDicOXqBE4M0nRWkUu0PTp++UPNzoFY12BNHMfs/VadKIS6llvg==}
    engines: {node: '>=8.3.0'}

  fast-csv@4.3.6:
    resolution: {integrity: sha512-2RNSpuwwsJGP0frGsOmTb9oUF+VkFSM4SyLTDgwf2ciHWTarN0lQTC+F2f/t5J9QjW+c65VFIAAu85GsvMIusw==}
    engines: {node: '>=10.0.0'}

  fbemitter@3.0.0:
    resolution: {integrity: sha512-KWKaceCwKQU0+HPoop6gn4eOHk50bBv/VxjJtGMfwmJt3D29JpN4H4eisCtIPA+a8GVBam+ldMMpMjJUvpDyHw==}

  fbjs-css-vars@1.0.2:
    resolution: {integrity: sha512-b2XGFAFdWZWg0phtAWLHCk836A1Xann+I+Dgd3Gk64MHKZO44FfoD1KxyvbSh0qZsIoXQGGlVztIY+oitJPpRQ==}

  fbjs@3.0.5:
    resolution: {integrity: sha512-ztsSx77JBtkuMrEypfhgc3cI0+0h+svqeie7xHbh1k/IKdcydnvadp/mUaGgjAOXQmQSxsqgaRhS3q9fy+1kxg==}

  fdir@6.5.0:
    resolution: {integrity: sha512-tIbYtZbucOs0BRGqPJkshJUYdL+SDH7dVM8gjy+ERp3WAUjLEFJE+02kanyHtwjWOnwrKYBiwAmM0p4kLJAnXg==}
    engines: {node: '>=12.0.0'}
    peerDependencies:
      picomatch: ^3 || ^4
    peerDependenciesMeta:
      picomatch:
        optional: true

  file-saver@2.0.5:
    resolution: {integrity: sha512-P9bmyZ3h/PRG+Nzga+rbdI4OEpNDzAVyy74uVO9ATgzLK6VtAsYybF/+TOCvrc0MO793d6+42lLyZTw7/ArVzA==}

  file-selector@0.4.0:
    resolution: {integrity: sha512-iACCiXeMYOvZqlF1kTiYINzgepRBymz1wwjiuup9u9nayhb6g4fSwiyJ/6adli+EPwrWtpgQAh2PoS7HukEGEg==}
    engines: {node: '>= 10'}

  file64@1.0.5:
    resolution: {integrity: sha512-GQLvUJk6RIMUJhULultcZ5cQfKUIgcBWmiYdVMXAAljhNJAmEj+H16LGiaR5ZbF+XEBm4CGjVFv5FzuL6EeceA==}
    engines: {node: '>=18'}

  flux@4.0.4:
    resolution: {integrity: sha512-NCj3XlayA2UsapRpM7va6wU1+9rE5FIL7qoMcmxWHRzbp0yujihMBm9BBHZ1MDIk5h5o2Bl6eGiCe8rYELAmYw==}
    peerDependencies:
      react: ^15.0.2 || ^16.0.0 || ^17.0.0

  follow-redirects@1.15.11:
    resolution: {integrity: sha512-deG2P0JfjrTxl50XGCDyfI97ZGVCxIpfKYmfyrQ54n5FO/0gfIES8C/Psl6kWVDolizcaaxZJnTS0QSMxvnsBQ==}
    engines: {node: '>=4.0'}
    peerDependencies:
      debug: '*'
    peerDependenciesMeta:
      debug:
        optional: true

  frac@1.1.2:
    resolution: {integrity: sha512-w/XBfkibaTl3YDqASwfDUqkna4Z2p9cFSr1aHDt0WoMTECnRfBOv2WArlZILlqgWlmdIlALXGpM2AOhEk5W3IA==}
    engines: {node: '>=0.8'}

  froala-editor@3.1.1:
    resolution: {integrity: sha512-1VYBEL5QN92Qmlvc1tgL6Ij55hMDXnkStv1c/MoNwdkTdK81W1JYmS4LaLFfUmva1Zvp5DWEM+G3G8+VyfvDEQ==}

  fs-constants@1.0.0:
    resolution: {integrity: sha512-y6OAwoSIf7FyjMIv94u+b5rdheZEjzR63GTyZJm5qh4Bi+2YgwLCcI/fPFZkL5PSixOt6ZNKm+w+Hfp/Bciwow==}

  fs-minipass@2.1.0:
    resolution: {integrity: sha512-V/JgOLFCS+R6Vcq0slCuaeWEdNC3ouDlJMNIsacH2VtALiu9mV4LPrHc5cDl8k5aw6J8jwgWWpiTo5RYhmIzvg==}
    engines: {node: '>= 8'}

  fs.realpath@1.0.0:
    resolution: {integrity: sha512-OO0pH2lK6a0hZnAdau5ItzHPI6pUlvI7jMVnxUQRtw4owF2wk8lOSabtGDCTP4Ggrg2MbGnWO9X8K1t4+fGMDw==}

  fsevents@2.3.3:
    resolution: {integrity: sha512-5xoDfX+fL7faATnagmWPpbFtwh/R77WmMMqqHGS65C3vvB0YHrgF+B1YmZ3441tMj5n63k0212XNoJwzlhffQw==}
    engines: {node: ^8.16.0 || ^10.6.0 || >=11.0.0}
    os: [darwin]

  fstream@1.0.12:
    resolution: {integrity: sha512-WvJ193OHa0GHPEL+AycEJgxvBEwyfRkN1vhjca23OaPVMCaLCXTd5qAu82AjTcgP1UJmytkOKb63Ypde7raDIg==}
    engines: {node: '>=0.6'}
    deprecated: This package is no longer supported.

  gauge@3.0.2:
    resolution: {integrity: sha512-+5J6MS/5XksCuXq++uFRsnUd7Ovu1XenbeuIuNRJxYWjgQbPuFhT14lAvsWfqfAmnwluf1OwMjz39HjfLPci0Q==}
    engines: {node: '>=10'}
    deprecated: This package is no longer supported.

  glob@7.2.3:
    resolution: {integrity: sha512-nFR0zLpU2YCaRxwoCJvL6UvCH2JFyFVIvwTLsIf21AuHlMskA1hhTdk+LlYJtOlYt9v6dvszD2BGRqBL+iQK9Q==}
    deprecated: Glob versions prior to v9 are no longer supported

  graceful-fs@4.2.11:
    resolution: {integrity: sha512-RbJ5/jmFcNNCcDV5o9eTnBLJ/HszWV0P73bc+Ff4nS/rJj+YaS6IGyiOL0VoBYX+l1Wrl3k63h/KrH+nhJ0XvQ==}

  has-unicode@2.0.1:
    resolution: {integrity: sha512-8Rf9Y83NBReMnx0gFzA8JImQACstCYWUplepDa9xprwwtmgEZUF0h/i5xSA625zB/I37EtrswSST6OXxwaaIJQ==}

  hls.js@1.1.3:
    resolution: {integrity: sha512-H1Gbbi5f786/pU5iS4IngRt4pgJvLDV+eD5iiqUFZVd62r0Uz0uxL+Lmx+idQIxwsg8krkLPK7p+EtMQhKk9hg==}

  hoist-non-react-statics@3.3.2:
    resolution: {integrity: sha512-/gGivxi8JPKWNm/W0jSmzcMPpfpPLc3dY/6GxhX2hQ9iGj3aDfklV4ET7NjKpSinLpJ5vafa9iiGIEZg10SfBw==}

  hotkeys-js@3.13.15:
    resolution: {integrity: sha512-gHh8a/cPTCpanraePpjRxyIlxDFrIhYqjuh01UHWEwDpglJKCnvLW8kqSx5gQtOuSsJogNZXLhOdbSExpgUiqg==}

  https-proxy-agent@5.0.1:
    resolution: {integrity: sha512-dFcAjpTQFgoLMzC2VwU+C/CbS7uRL0lWmxDITmqm7C+7F0Odmj6s9l6alZc6AELXhrnggM2CeWSXHGOdX2YtwA==}
    engines: {node: '>= 6'}

  i18n-runtime@1.0.10:
    resolution: {integrity: sha512-MW51npNjUT9TBns/L3+GI9NreZ4uWJ/Sk9XokFiVyym+8ruxUghhVT9NovTQpZaPk3dQ8zt3o6wz0bO7kUi5WQ==}
    peerDependencies:
      amis-core: '*'

  ieee754@1.2.1:
    resolution: {integrity: sha512-dcyqhDvX1C46lXZcVqCpK+FtMRQVdIMN6/Df5js2zouUsqG7I6sFxitIC+7KYK29KdXOLHdu9zL4sFnoVQnqaA==}

  immediate@3.0.6:
    resolution: {integrity: sha512-XXOFtyqDjNDAQxVfYxuF7g9Il/IbWmmlQg2MYKOH8ExIT1qg6xc4zyS3HaEEATgs1btfzxq15ciUiY7gjSXRGQ==}

  immutability-helper@3.1.1:
    resolution: {integrity: sha512-Q0QaXjPjwIju/28TsugCHNEASwoCcJSyJV3uO1sOIQGI0jKgm9f41Lvz0DZj3n46cNCyAZTsEYoY4C2bVRUzyQ==}

  inflight@1.0.6:
    resolution: {integrity: sha512-k92I/b08q4wvFscXCLvqfsHCrjrF7yiXsQuIVvVE7N82W3+aqpzuUdBbfhWcy/FZR3/4IgflMgKLOsvPDrGCJA==}
    deprecated: This module is not supported, and leaks memory. Do not use it. Check out lru-cache if you want a good and tested way to coalesce async requests by a key value, which is much more comprehensive and powerful.

  inherits@2.0.4:
    resolution: {integrity: sha512-k/vGaX4/Yla3WzyMCvTQOXYeIHvqOKtnqBduzTHpzpQZzAskKMhZ2K+EnBiSM9zGSoIFeMpXKxa4dYeZIQqewQ==}

  invariant@2.2.4:
    resolution: {integrity: sha512-phJfQVBuaJM5raOpJjSfkiD6BpbCE4Ns//LaXl6wGYtUBY83nWS6Rf9tXm2e8VaK60JEjYldbPif/A2B1C2gNA==}

  is-fullwidth-code-point@3.0.0:
    resolution: {integrity: sha512-zymm5+u+sCsSWyD9qNaejV3DFvhCKclKdizYaJUuHA83RLjb7nSuGnddCHGv0hk+KY7BMAlsWeK4Ueg6EV6XQg==}
    engines: {node: '>=8'}

  isarray@1.0.0:
    resolution: {integrity: sha512-VLghIWNM6ELQzo7zwmcg0NmTVyWKYjvIeM83yjp0wRDTmUnrM678fQbcKBo6n2CJEF0szoG//ytg+TKla89ALQ==}

  isexe@2.0.0:
    resolution: {integrity: sha512-RHxMLp9lnKHGHRng9QFhRCMbYAcVpn69smSGcq3f36xjgVVWThj4qqLbTLlq7Ssj8B+fIQ1EuCEGI2lKsyQeIw==}

  js-tokens@4.0.0:
    resolution: {integrity: sha512-RdJUflcE3cUzKiMqQgsCu06FPu9UdIJO0beYbPhHN4k6apgJtifcoCtT9bcxOpYBtpD2kCM6Sbzg4CausW/PKQ==}

  jsbarcode@3.12.1:
    resolution: {integrity: sha512-QZQSqIknC2Rr/YOUyOkCBqsoiBAOTYK+7yNN3JsqfoUtJtkazxNw1dmPpxuv7VVvqW13kA3/mKiLq+s/e3o9hQ==}

  json-ast-comments@1.1.1:
    resolution: {integrity: sha512-UOHlf7ns5t1GiI3+T5tf9SN2OepXTo/sqhd+cQj++DaUMKQOOCbX+eRlIoHcEv9m902reDTc1mCJD4J69xFJSg==}

  jszip@3.10.1:
    resolution: {integrity: sha512-xXDvecyTpGLrqFrvkrUSoxxfJI5AH7U8zxxtVclpsUtMCq4JQ290LY8AW5c7Ggnr/Y/oK+bQMbqK2qmtk3pN4g==}

  keycode@2.2.1:
    resolution: {integrity: sha512-Rdgz9Hl9Iv4QKi8b0OlCRQEzp4AgVxyCtz5S/+VIHezDmrDhkp2N2TqBWOLz0/gbeREXOOiI9/4b8BY9uw2vFg==}

  lazystream@1.0.1:
    resolution: {integrity: sha512-b94GiNHQNy6JNTrt5w6zNyffMrNkXZb3KTkCZJb2V1xaEGCk093vkZ2jk3tpaeP33/OiXC+WvK9AxUebnf5nbw==}
    engines: {node: '>= 0.6.3'}

  lie@3.3.0:
    resolution: {integrity: sha512-UaiMJzeWRlEujzAuw5LokY1L5ecNQYZKfmyZ9L7wDHb/p5etKaxXhohBcrw0EYby+G/NA52vRSN4N39dxHAIwQ==}

  linkify-it@3.0.3:
    resolution: {integrity: sha512-ynTsyrFSdE5oZ/O9GEf00kPngmOfVwazR5GKDq6EYfhlpFug3J2zybX56a2PRRpc9P+FuSoGNAwjlbDs9jJBPQ==}

  listenercount@1.0.1:
    resolution: {integrity: sha512-3mk/Zag0+IJxeDrxSgaDPy4zZ3w05PRZeJNnlWhzFz5OkX49J4krc+A8X2d2M69vGMBEX0uyl8M+W+8gH+kBqQ==}

  lodash-es@4.17.21:
    resolution: {integrity: sha512-mKnC+QJ9pWVzv+C4/U3rRsHapFfHvQFoFB92e52xeyGMcX6/OlIl78je1u8vePzYZSkkogMPJ2yjxxsb89cxyw==}

  lodash.curry@4.1.1:
    resolution: {integrity: sha512-/u14pXGviLaweY5JI0IUzgzF2J6Ne8INyzAZjImcryjgkZ+ebruBxy2/JaOOkTqScddcYtakjhSaeemV8lR0tA==}

  lodash.defaults@4.2.0:
    resolution: {integrity: sha512-qjxPLHd3r5DnsdGacqOMU6pb/avJzdh9tFX2ymgoZE27BmjXrNy/y4LoaiTeAb+O3gL8AfpJGtqfX/ae2leYYQ==}

  lodash.difference@4.5.0:
    resolution: {integrity: sha512-dS2j+W26TQ7taQBGN8Lbbq04ssV3emRw4NY58WErlTO29pIqS0HmoT5aJ9+TUQ1N3G+JOZSji4eugsWwGp9yPA==}

  lodash.escaperegexp@4.1.2:
    resolution: {integrity: sha512-TM9YBvyC84ZxE3rgfefxUWiQKLilstD6k7PTGt6wfbtXF8ixIJLOL3VYyV/z+ZiPLsVxAsKAFVwWlWeb2Y8Yyw==}

  lodash.flatten@4.4.0:
    resolution: {integrity: sha512-C5N2Z3DgnnKr0LOpv/hKCgKdb7ZZwafIrsesve6lmzvZIRZRGaZ/l6Q8+2W7NaT+ZwO3fFlSCzCzrDCFdJfZ4g==}

  lodash.flow@3.5.0:
    resolution: {integrity: sha512-ff3BX/tSioo+XojX4MOsOMhJw0nZoUEF011LX8g8d3gvjVbxd89cCio4BCXronjxcTUIJUoqKEUA+n4CqvvRPw==}

  lodash.groupby@4.6.0:
    resolution: {integrity: sha512-5dcWxm23+VAoz+awKmBaiBvzox8+RqMgFhi7UvX9DHZr2HdxHXM/Wrf8cfKpsW37RNrvtPn6hSwNqurSILbmJw==}

  lodash.isboolean@3.0.3:
    resolution: {integrity: sha512-Bz5mupy2SVbPHURB98VAcw+aHh4vRV5IPNhILUCsOzRmsTmSQ17jIuqopAentWoehktxGd9e/hbIXq980/1QJg==}

  lodash.isequal@4.5.0:
    resolution: {integrity: sha512-pDo3lu8Jhfjqls6GkMgpahsF9kCyayhgykjyLMNFTKWrpVdAQtYyB4muAMWozBB4ig/dtWAmsMxLEI8wuz+DYQ==}
    deprecated: This package is deprecated. Use require('node:util').isDeepStrictEqual instead.

  lodash.isfunction@3.0.9:
    resolution: {integrity: sha512-AirXNj15uRIMMPihnkInB4i3NHeb4iBtNg9WRWuK2o31S+ePwwNmDPaTL3o7dTJ+VXNZim7rFs4rxN4YU1oUJw==}

  lodash.isnil@4.0.0:
    resolution: {integrity: sha512-up2Mzq3545mwVnMhTDMdfoG1OurpA/s5t88JmQX809eH3C8491iu2sfKhTfhQtKY78oPNhiaHJUpT/dUDAAtng==}

  lodash.isplainobject@4.0.6:
    resolution: {integrity: sha512-oSXzaWypCMHkPC3NvBEaPHf0KsA5mvPrOPgQWDsbg8n7orZ290M0BmC/jgRZ4vcJ6DTAhjrsSYgdsW/F+MFOBA==}

  lodash.isundefined@3.0.1:
    resolution: {integrity: sha512-MXB1is3s899/cD8jheYYE2V9qTHwKvt+npCwpD+1Sxm3Q3cECXCiYHjeHWXNwr6Q0SOBPrYUDxendrO6goVTEA==}

  lodash.throttle@4.1.1:
    resolution: {integrity: sha512-wIkUCfVKpVsWo3JSZlc+8MB5it+2AN5W8J7YVMST30UrvcQNZ1Okbj+rbVniijTWE6FGYy4XJq/rHkas8qJMLQ==}

  lodash.union@4.6.0:
    resolution: {integrity: sha512-c4pB2CdGrGdjMKYLA+XiRDO7Y0PRQbm/Gzg8qMj+QH+pFVAoTp5sBpO0odL3FjoPCGjK96p6qsP+yQoiLoOBcw==}

  lodash.uniq@4.5.0:
    resolution: {integrity: sha512-xfBaXQd9ryd9dlSDvnvI0lvxfLJlYAZzXomUYzLKtUeOQvOP5piqAWuGtrhWeqaXK9hhoM/iyJc5AV+XfsX3HQ==}

  lodash@4.17.21:
    resolution: {integrity: sha512-v2kDEe57lecTulaDIuNTPy3Ry4gLGJ6Z1O3vE1krgXZNrsQ+LFTGHVxVjcXPs17LhbZVGedAJv8XZ1tvj5FvSg==}

  loose-envify@1.4.0:
    resolution: {integrity: sha512-lyuxPGr/Wfhrlem2CL/UcnUc1zcqKAImBDzukY7Y5F/yQiNdko6+fRLevlw1HgMySw7f611UIY408EtxRSoK3Q==}
    hasBin: true

  magic-string@0.30.18:
    resolution: {integrity: sha512-yi8swmWbO17qHhwIBNeeZxTceJMeBvWJaId6dyvTSOwTipqeHhMhOrz6513r1sOKnpvQ7zkhlG8tPrpilwTxHQ==}

  make-cancellable-promise@1.3.2:
    resolution: {integrity: sha512-GCXh3bq/WuMbS+Ky4JBPW1hYTOU+znU+Q5m9Pu+pI8EoUqIHk9+tviOKC6/qhHh8C4/As3tzJ69IF32kdz85ww==}

  make-dir@3.1.0:
    resolution: {integrity: sha512-g3FeP20LNwhALb/6Cz6Dd4F2ngze0jz7tbzrD2wAV+o9FeNHe4rL+yK2md0J/fiSf1sa1ADhXqi5+oVwOM/eGw==}
    engines: {node: '>=8'}

  make-event-props@1.6.2:
    resolution: {integrity: sha512-iDwf7mA03WPiR8QxvcVHmVWEPfMY1RZXerDVNCRYW7dUr2ppH3J58Rwb39/WG39yTZdRSxr3x+2v22tvI0VEvA==}

  markdown-it-html5-media@0.7.1:
    resolution: {integrity: sha512-PfKFD+K1Vpw8+OwfV/VO6Y5Sy1xSS/F1BgbW8Q281LylBBoW47fm4k9Zh0IpsPpM3/HObBD6rooMTaMVlC3PqA==}
    engines: {node: '>=10.0'}

  markdown-it@12.3.2:
    resolution: {integrity: sha512-TchMembfxfNVpHkbtriWltGWc+m3xszaRD0CZup7GFFhzIgQqxIfn3eGj1yZpfuflzPvfkt611B2Q/Bsk1YnGg==}
    hasBin: true

  match-sorter@6.3.4:
    resolution: {integrity: sha512-jfZW7cWS5y/1xswZo8VBOdudUiSd9nifYRWphc9M5D/ee4w4AoXLgBEdRbgVaxbMuagBPeUC5y2Hi8DO6o9aDg==}

  material-colors@1.2.6:
    resolution: {integrity: sha512-6qE4B9deFBIa9YSpOc9O0Sgc43zTeVYbgDT5veRKSlB2+ZuHNoVVxA1L/ckMUayV9Ay9y7Z/SZCLcGteW9i7bg==}

  mdurl@1.0.1:
    resolution: {integrity: sha512-/sKlQJCBYVY9Ers9hqzKou4H6V5UWc/M59TH2dvkt+84itfnq7uFOMLpOiOS4ujvHP4etln18fmIxA5R5fll0g==}

  merge-refs@1.3.0:
    resolution: {integrity: sha512-nqXPXbso+1dcKDpPCXvwZyJILz+vSLqGGOnDrYHQYE+B8n9JTCekVLC65AfCpR4ggVyA/45Y0iR9LDyS2iI+zA==}
    peerDependencies:
      '@types/react': ^16.8.0 || ^17.0.0 || ^18.0.0 || ^19.0.0
    peerDependenciesMeta:
      '@types/react':
        optional: true

  mimic-response@2.1.0:
    resolution: {integrity: sha512-wXqjST+SLt7R009ySCglWBCFpjUygmCIfD790/kVbiGmUgfYGuB14PiTd5DwVxSV4NcYHjzMkoj5LjQZwTQLEA==}
    engines: {node: '>=8'}

  minimatch@3.1.2:
    resolution: {integrity: sha512-J7p63hRiAjw1NDEww1W7i37+ByIrOWO5XQQAzZ3VOcL0PNybwpfmV/N05zFAzwQ9USyEcX6t3UO+K5aqBQOIHw==}

  minimatch@5.1.6:
    resolution: {integrity: sha512-lKwV/1brpG6mBUFHtb7NUmtABCb2WZZmm2wNiOA5hAb8VdCS4B3dtMWyvcoViccwAW/COERjXLt0zP1zXUN26g==}
    engines: {node: '>=10'}

  minimist@1.2.8:
    resolution: {integrity: sha512-2yyAR8qBkN3YuheJanUpWC5U3bb5osDywNB8RzDVlDwDHbocAJveqqj1u8+SVD7jkWT4yvsHCpWqqWqAxb0zCA==}

  minipass@3.3.6:
    resolution: {integrity: sha512-DxiNidxSEK+tHG6zOIklvNOwm3hvCrbUrdtzY74U6HKTJxvIDfOUL5W5P2Ghd3DTkhhKPYGqeNUIh5qcM4YBfw==}
    engines: {node: '>=8'}

  minipass@5.0.0:
    resolution: {integrity: sha512-3FnjYuehv9k6ovOEbyOswadCDPX1piCfhV8ncmYtHOjuPwylVWsghTLo7rabjC3Rx5xD4HDx8Wm1xnMF7S5qFQ==}
    engines: {node: '>=8'}

  minizlib@2.1.2:
    resolution: {integrity: sha512-bAxsR8BVfj60DWXHE3u30oHzfl4G7khkSuPW+qvpd7jFRHm7dLxOjUk1EHACJ/hxLY8phGJ0YhYHZo7jil7Qdg==}
    engines: {node: '>= 8'}

  mkdirp@0.5.6:
    resolution: {integrity: sha512-FP+p8RB8OWpF3YZBCrP5gtADmtXApB5AMLn+vdyA+PyxCjrCs00mjyUozssO33cwDeT3wNGdLxJ5M//YqtHAJw==}
    hasBin: true

  mkdirp@1.0.4:
    resolution: {integrity: sha512-vVqVZQyf3WLx2Shd0qJ9xuvqgAyKPLAiqITEtqW0oIUjzo3PePDd6fW9iFz30ef7Ysp/oiWqbhszeGWW2T6Gzw==}
    engines: {node: '>=10'}
    hasBin: true

  mobx-react-lite@2.2.2:
    resolution: {integrity: sha512-2SlXALHIkyUPDsV4VTKVR9DW7K3Ksh1aaIv3NrNJygTbhXe2A9GrcKHZ2ovIiOp/BXilOcTYemfHHZubP431dg==}
    peerDependencies:
      mobx: ^4.0.0 || ^5.0.0
      react: ^16.8.0
      react-dom: '*'
      react-native: '*'
    peerDependenciesMeta:
      react-dom:
        optional: true
      react-native:
        optional: true

  mobx-react@6.3.1:
    resolution: {integrity: sha512-IOxdJGnRSNSJrL2uGpWO5w9JH5q5HoxEqwOF4gye1gmZYdjoYkkMzSGMDnRCUpN/BNzZcFoMdHXrjvkwO7KgaQ==}
    peerDependencies:
      mobx: ^5.15.4 || ^4.15.4
      react: ^16.8.0 || 16.9.0-alpha.0

  mobx-state-tree@3.17.3:
    resolution: {integrity: sha512-ph4ee/Lh1qUJqHEGkfdWdBAUGdG+VAu7xZbYX/+4qem5hSSpdeZYAJOcN3bhtgEH8Wh/ZxRpQVOLM0aMFXfBSw==}
    peerDependencies:
      mobx: '>=4.8.0 <5.0.0 || >=5.8.0 <6.0.0'

  mobx@4.15.7:
    resolution: {integrity: sha512-X4uQvuf2zYKHVO5kRT5Utmr+J9fDnRgxWWnSqJ4oiccPTQU38YG+/O3nPmOhUy4jeHexl7XJJpWDBgEnEfp+8w==}

  moment-timezone@0.5.48:
    resolution: {integrity: sha512-f22b8LV1gbTO2ms2j2z13MuPogNoh5UzxL3nzNAYKGraILnbGc9NEE6dyiiiLv46DGRb8A4kg8UKWLjPthxBHw==}

  moment@2.30.1:
    resolution: {integrity: sha512-uEmtNhbDOrWPFS+hdjFCBfy9f2YoyzRpwcl+DqpC6taX21FzsTLQVbMV/W7PzNSX6x/bhC1zA3c2UQ5NzH6how==}

  monaco-editor@0.30.1:
    resolution: {integrity: sha512-B/y4+b2O5G2gjuxIFtCE2EkM17R2NM7/3F8x0qcPsqy4V83bitJTIO4TIeZpYlzu/xy6INiY/+84BEm6+7Cmzg==}

  monaco-editor@0.52.2:
    resolution: {integrity: sha512-GEQWEZmfkOGLdd3XK8ryrfWz3AIP8YymVXiPHEdewrUq7mh0qrKrfHLNCXcbB6sTnMLnOZ3ztSiKcciFUkIJwQ==}

  mpegts.js@1.8.0:
    resolution: {integrity: sha512-ZtujqtmTjWgcDDkoOnLvrOKUTO/MKgLHM432zGDI8oPaJ0S+ebPxg1nEpDpLw6I7KmV/GZgUIrfbWi3qqEircg==}

  ms@2.1.3:
    resolution: {integrity: sha512-6FlzubTLZG3J2a/NVCAleEhjzq5oxgHyaCU9yYXvcLsvoVaHJq/s5xXI6/XXP6tz7R9xAOtHnSO/tXtF3WRTlA==}

  nan@2.23.0:
    resolution: {integrity: sha512-1UxuyYGdoQHcGg87Lkqm3FzefucTa0NAiOcuRsDmysep3c1LVCRK2krrUDafMWtjSG04htvAmvg96+SDknOmgQ==}

  nanoid@3.3.11:
    resolution: {integrity: sha512-N8SpfPUnUp1bK+PMYW8qSWdl9U+wwNWI4QKxOYDy9JAro3WMX7p2OeVRF9v+347pnakNevPmiHhNmZ2HbFA76w==}
    engines: {node: ^10 || ^12 || ^13.7 || ^14 || >=15.0.1}
    hasBin: true

  node-fetch@2.7.0:
    resolution: {integrity: sha512-c4FRfUm/dbcWZ7U+1Wq0AwCyFL+3nt2bEw05wfxSz+DWpWsitgmSgYmy2dQdWyKC1694ELPqMs/YzUSNozLt8A==}
    engines: {node: 4.x || >=6.0.0}
    peerDependencies:
      encoding: ^0.1.0
    peerDependenciesMeta:
      encoding:
        optional: true

  nopt@5.0.0:
    resolution: {integrity: sha512-Tbj67rffqceeLpcRXrT7vKAN8CwfPeIBgM7E6iBkmKLV7bEMwpGgYLGv0jACUsECaa/vuxP0IjEont6umdMgtQ==}
    engines: {node: '>=6'}
    hasBin: true

  normalize-path@3.0.0:
    resolution: {integrity: sha512-6eZs5Ls3WtCisHWp9S2GUy8dqkpGi4BVSz3GaqiE6ezub0512ESztXUwUB6C6IKbQkY2Pnb/mD4WYojCRwcwLA==}
    engines: {node: '>=0.10.0'}

  npmlog@5.0.1:
    resolution: {integrity: sha512-AqZtDUWOMKs1G/8lwylVjrdYgqA4d9nu8hc+0gzRxlDb1I10+FHBGMXs6aiQHFdCUUlqH99MUMuLfzWDNDtfxw==}
    deprecated: This package is no longer supported.

  numfmt@2.5.2:
    resolution: {integrity: sha512-VXrB2bpU9Xa0oCHq8IsqE2CcUx5OLupLC3oryFT4DB9e/xe+OnUzBndhXfNHUzxFE4DYI3Sx4OtzS1Sdaf7tEw==}

  object-assign@4.1.1:
    resolution: {integrity: sha512-rJgTQnkUnH1sFw8yT6VSU3zD3sWmu6sZhIseY8VX+GRu3P6F7Fu+JNDoXfklElbLJSnc3FUQHVe4cU5hj+BcUg==}
    engines: {node: '>=0.10.0'}

  office-viewer@0.3.14:
    resolution: {integrity: sha512-nm4b1LXvRbgfTllOGOAGNOrX5tAEqv3jxF0YKe5QbeNQhHagd1WTXknqghSGBURyrGTRXsX9zSgbzpu4/XV8dQ==}
    peerDependencies:
      echarts: ^5.4.0

  once@1.4.0:
    resolution: {integrity: sha512-lNaJgI+2Q5URQBkccEKHTQOPaXdUxnZZElQTZY0MFUAuaEqe1E+Nyvgdz/aIyNi6Z9MzO5dv1H8n58/GELp3+w==}

  pako@1.0.11:
    resolution: {integrity: sha512-4hLB8Py4zZce5s4yd9XzopqwVv/yGNhV1Bl8NTmCq1763HeK2+EwVTv+leGeL13Dnh2wfbqowVPXCIO0z4taYw==}

  papaparse@5.5.3:
    resolution: {integrity: sha512-5QvjGxYVjxO59MGU2lHVYpRWBBtKHnlIAcSe1uNFCkkptUh63NFRj0FJQm7nR67puEruUci/ZkjmEFrjCAyP4A==}

  path-is-absolute@1.0.1:
    resolution: {integrity: sha512-AVbw3UJ2e9bq64vSaS9Am0fje1Pa8pbGqTTsmXfaIiMpnr5DlDhfJOuLj9Sf95ZPVDAUerDfEk88MPmPe7UCQg==}
    engines: {node: '>=0.10.0'}

  path-key@3.1.1:
    resolution: {integrity: sha512-ojmeN0qd+y0jszEtoY48r0Peq5dwMEkIlCOu6Q5f41lfkswXuKtYrhgoTpLnyIcHm24Uhqx+5Tqm2InSwLhE6Q==}
    engines: {node: '>=8'}

  path-to-regexp@6.2.0:
    resolution: {integrity: sha512-f66KywYG6+43afgE/8j/GoiNyygk/bnoCbps++3ErRKsIYkGGupyv07R2Ok5m9i67Iqc+T2g1eAUGUPzWhYTyg==}

  path2d@0.2.2:
    resolution: {integrity: sha512-+vnG6S4dYcYxZd+CZxzXCNKdELYZSKfohrk98yajCo1PtRoDgCTrrwOvK1GT0UoAdVszagDVllQc0U1vaX4NUQ==}
    engines: {node: '>=6'}

  pdfjs-dist@4.3.136:
    resolution: {integrity: sha512-gzfnt1qc4yA+U46golPGYtU4WM2ssqP2MvFjKga8GEKOrEnzRPrA/9jogLLPYHiA3sGBPJ+p7BdAq+ytmw3jEg==}
    engines: {node: '>=18'}

  picocolors@1.1.1:
    resolution: {integrity: sha512-xceH2snhtb5M9liqDsmEw56le376mTZkEX/jEb/RxNFyegNul7eNslCXP9FDj/Lcu0X8KEyMceP2ntpaHrDEVA==}

  picomatch@4.0.3:
    resolution: {integrity: sha512-5gTmgEY/sqK6gFXLIsQNH19lWb4ebPDLA4SdLP7dsWkIXHWlG66oPuVvXSGFPppYZz8ZDZq0dYYrbHfBCVUb1Q==}
    engines: {node: '>=12'}

  postcss@8.5.6:
    resolution: {integrity: sha512-3Ybi1tAuwAP9s0r1UQ2J4n5Y0G05bJkpUIO0/bI9MhwmD70S5aTWbXGBwxHrelT+XM1k6dM0pk+SwNkpTRN7Pg==}
    engines: {node: ^10 || ^12 || >=14}

  process-nextick-args@2.0.1:
    resolution: {integrity: sha512-3ouUOpQhtgrbOa17J7+uxOTpITYWaGP7/AhoR3+A+/1e9skrzelGi/dXzEYyvbxubEF6Wn2ypscTKiKJFFn1ag==}

  promise@7.3.1:
    resolution: {integrity: sha512-nolQXZ/4L+bP/UGlkfaIujX9BKxGwmQ9OT4mOt5yvy8iK1h3wqTEJCijzGANTCCl9nWjY41juyAn2K3Q1hLLTg==}

  prop-types@15.8.1:
    resolution: {integrity: sha512-oj87CgZICdulUohogVAR7AjlC0327U4el4L6eAvOqCeudMDVU0NThNaV+b9Df4dXgSP1gXMTnPdhfe/2qDH5cg==}

  pure-color@1.3.0:
    resolution: {integrity: sha512-QFADYnsVoBMw1srW7OVKEYjG+MbIa49s54w1MA1EDY6r2r/sTcKKYqRX1f4GYvnXP7eN/Pe9HFcX+hwzmrXRHA==}

  qrcode-react-next@1.0.0:
    resolution: {integrity: sha512-d8U0xudKLDJDGdUFWKyNivBcf0M0VSwbcXChIA8cfFHkB51SlxlCtrwbJYukzV1qVBexDtB0GfXm0oQvh0Pwdw==}
    peerDependencies:
      react: '>16.0.0'

  qs@6.9.7:
    resolution: {integrity: sha512-IhMFgUmuNpyRfxA90umL7ByLlgRXu6tIfKPpF5TmcfRLlLCckfP/g3IQmju6jjpu+Hh8rA+2p6A27ZSPOOHdKw==}
    engines: {node: '>=0.6'}

  rc-input-number@7.4.2:
    resolution: {integrity: sha512-yGturTw7WGP+M1GbJ+UTAO7L4buxeW6oilhL9Sq3DezsRS8/9qec4UiXUbeoiX9bzvRXH11JvgskBtxSp4YSNg==}
    peerDependencies:
      react: '>=16.9.0'
      react-dom: '>=16.9.0'

  rc-menu@9.16.1:
    resolution: {integrity: sha512-ghHx6/6Dvp+fw8CJhDUHFHDJ84hJE3BXNCzSgLdmNiFErWSOaZNsihDAsKq9ByTALo/xkNIwtDFGIl6r+RPXBg==}
    peerDependencies:
      react: '>=16.9.0'
      react-dom: '>=16.9.0'

  rc-motion@2.9.5:
    resolution: {integrity: sha512-w+XTUrfh7ArbYEd2582uDrEhmBHwK1ZENJiSJVb7uRxdE7qJSYjbO2eksRXmndqyKqKoYPc9ClpPh5242mV1vA==}
    peerDependencies:
      react: '>=16.9.0'
      react-dom: '>=16.9.0'

  rc-overflow@1.4.1:
    resolution: {integrity: sha512-3MoPQQPV1uKyOMVNd6SZfONi+f3st0r8PksexIdBTeIYbMX0Jr+k7pHEDvsXtR4BpCv90/Pv2MovVNhktKrwvw==}
    peerDependencies:
      react: '>=16.9.0'
      react-dom: '>=16.9.0'

  rc-progress@3.4.2:
    resolution: {integrity: sha512-iAGhwWU+tsayP+Jkl9T4+6rHeQTG9kDz8JAHZk4XtQOcYN5fj9H34NXNEdRdZx94VUDHMqCb1yOIvi8eJRh67w==}
    peerDependencies:
      react: '>=16.9.0'
      react-dom: '>=16.9.0'

  rc-resize-observer@1.4.3:
    resolution: {integrity: sha512-YZLjUbyIWox8E9i9C3Tm7ia+W7euPItNWSPX5sCcQTYbnwDb5uNpnLHQCG1f22oZWUhLw4Mv2tFmeWe68CDQRQ==}
    peerDependencies:
      react: '>=16.9.0'
      react-dom: '>=16.9.0'

  rc-util@5.44.4:
    resolution: {integrity: sha512-resueRJzmHG9Q6rI/DfK6Kdv9/Lfls05vzMs1Sk3M2P+3cJa+MakaZyWY8IPfehVuhPJFKrIY1IK4GqbiaiY5w==}
    peerDependencies:
      react: '>=16.9.0'
      react-dom: '>=16.9.0'

  react-base16-styling@0.6.0:
    resolution: {integrity: sha512-yvh/7CArceR/jNATXOKDlvTnPKPmGZz7zsenQ3jUwLzHkNUR0CvY3yGYJbWJ/nnxsL8Sgmt5cO3/SILVuPO6TQ==}

  react-color@2.19.3:
    resolution: {integrity: sha512-LEeGE/ZzNLIsFWa1TMe8y5VYqr7bibneWmvJwm1pCn/eNmrabWDh659JSPn9BuaMpEfU83WTOJfnCcjDZwNQTA==}
    peerDependencies:
      react: '*'

  react-cropper@2.3.3:
    resolution: {integrity: sha512-zghiEYkUb41kqtu+2jpX2Ntigf+Jj1dF9ew4lAobPzI2adaPE31z0p+5TcWngK6TvmWQUwK3lj4G+NDh1PDQ1w==}
    peerDependencies:
      react: '>=17.0.2'

  react-dom@17.0.2:
    resolution: {integrity: sha512-s4h96KtLDUQlsENhMn1ar8t2bEa+q/YAtj8pPPdIjPDGBDIVNsrD9aXNWqspUe6AzKCIG0C1HZZLqLV7qpOBGA==}
    peerDependencies:
      react: 17.0.2

  react-draggable@4.5.0:
    resolution: {integrity: sha512-VC+HBLEZ0XJxnOxVAZsdRi8rD04Iz3SiiKOoYzamjylUcju/hP9np/aZdLHf/7WOD268WMoNJMvYfB5yAK45cw==}
    peerDependencies:
      react: '>= 16.3.0'
      react-dom: '>= 16.3.0'

  react-dropzone@11.7.1:
    resolution: {integrity: sha512-zxCMwhfPy1olUEbw3FLNPLhAm/HnaYH5aELIEglRbqabizKAdHs0h+WuyOpmA+v1JXn0++fpQDdNfUagWt5hJQ==}
    engines: {node: '>= 10.13'}
    peerDependencies:
      react: '>= 16.8'

  react-frame-component@5.2.7:
    resolution: {integrity: sha512-ROjHtSLoSVYUBfTieazj/nL8jIX9rZFmHC0yXEU+dx6Y82OcBEGgU9o7VyHMrBFUN9FuQ849MtIPNNLsb4krbg==}
    peerDependencies:
      prop-types: ^15.5.9
      react: '>= 16.3'
      react-dom: '>= 16.3'

  react-hook-form@7.39.0:
    resolution: {integrity: sha512-rekW5NMBVG0nslE2choOKThy0zxLWQeoew87yTLwb3C9F91LaXwu/dhfFL/D3hdnSMnrTG60gVN/v6rvCrSOTw==}
    engines: {node: '>=12.22.0'}
    peerDependencies:
      react: ^16.8.0 || ^17 || ^18

  react-intersection-observer@9.5.2:
    resolution: {integrity: sha512-EmoV66/yvksJcGa1rdW0nDNc4I1RifDWkT50gXSFnPLYQ4xUptuDD4V7k+Rj1OgVAlww628KLGcxPXFlOkkU/Q==}
    peerDependencies:
      react: ^15.0.0 || ^16.0.0 || ^17.0.0 || ^18.0.0

  react-is@16.13.1:
    resolution: {integrity: sha512-24e6ynE2H+OKt4kqsOvNd8kBpV65zoxbA4BVsEOB3ARVWQki/DHzaUoC5KuON/BiccDaCCTZBuOcfZs70kR8bQ==}

  react-is@17.0.2:
    resolution: {integrity: sha512-w2GsyukL62IJnlaff/nRegPQR94C/XXamvMWmSHRJ4y7Ts/4ocGRmTHvOs8PSE6pB3dWOrD/nueuU5sduBsQ4w==}

  react-is@18.3.1:
    resolution: {integrity: sha512-/LLMVyas0ljjAtoYiPqYiL8VWXzUUdThrmU5+n20DZv+a+ClRoevUzw5JxU+Ieh5/c87ytoTBV9G1FiKfNJdmg==}

  react-json-view@1.21.3:
    resolution: {integrity: sha512-13p8IREj9/x/Ye4WI/JpjhoIwuzEgUAtgJZNBJckfzJt1qyh24BdTm6UQNGnyTq9dapQdrqvquZTo3dz1X6Cjw==}
    peerDependencies:
      react: ^17.0.0 || ^16.3.0 || ^15.5.4
      react-dom: ^17.0.0 || ^16.3.0 || ^15.5.4

  react-lifecycles-compat@3.0.4:
    resolution: {integrity: sha512-fBASbA6LnOU9dOU2eW7aQ8xmYBSXUIWr+UmF9b1efZBazGNO+rcXT/icdKnYm2pTwcRylVUYwW7H1PHfLekVzA==}

  react-overlays@5.1.1:
    resolution: {integrity: sha512-eCN2s2/+GVZzpnId4XVWtvDPYYBD2EtOGP74hE+8yDskPzFy9+pV1H3ZZihxuRdEbQzzacySaaDkR7xE0ydl4Q==}
    peerDependencies:
      react: '>=16.3.0'
      react-dom: '>=16.3.0'

  react-pdf@9.0.0:
    resolution: {integrity: sha512-J+pza8R2p9oNEOJOHIQJI4o5rFK7ji7bBl2IvsHvz1OOyphvuzVDo5tOJwWAFAbxYauCH3Kt8jOvcMJUOpxYZQ==}
    peerDependencies:
      '@types/react': ^16.8.0 || ^17.0.0 || ^18.0.0 || ^19.0.0
      react: ^16.8.0 || ^17.0.0 || ^18.0.0 || ^19.0.0
      react-dom: ^16.8.0 || ^17.0.0 || ^18.0.0 || ^19.0.0
    peerDependenciesMeta:
      '@types/react':
        optional: true

  react-textarea-autosize@8.3.3:
    resolution: {integrity: sha512-2XlHXK2TDxS6vbQaoPbMOfQ8GK7+irc2fVK6QFIcC8GOnH3zI/v481n+j1L0WaPVvKxwesnY93fEfH++sus2rQ==}
    engines: {node: '>=10'}
    peerDependencies:
      react: ^16.8.0 || ^17.0.0

  react-textarea-autosize@8.5.9:
    resolution: {integrity: sha512-U1DGlIQN5AwgjTyOEnI1oCcMuEr1pv1qOtklB2l4nyMGbHzWrI0eFsYK0zos2YWqAolJyG0IWJaqWmWj5ETh0A==}
    engines: {node: '>=10'}
    peerDependencies:
      react: ^16.8.0 || ^17.0.0 || ^18.0.0 || ^19.0.0

  react-transition-group@4.4.2:
    resolution: {integrity: sha512-/RNYfRAMlZwDSr6z4zNKV6xu53/e2BuaBbGhbyYIXTrmgu/bGHzmqOs7mJSJBHy9Ud+ApHx3QjrkKSp1pxvlFg==}
    peerDependencies:
      react: '>=16.6.0'
      react-dom: '>=16.6.0'

  react-visibility-sensor@5.1.1:
    resolution: {integrity: sha512-cTUHqIK+zDYpeK19rzW6zF9YfT4486TIgizZW53wEZ+/GPBbK7cNS0EHyJVyHYacwFEvvHLEKfgJndbemWhB/w==}
    peerDependencies:
      react: '>=16.0.0'
      react-dom: '>=16.0.0'

  react@16.14.0:
    resolution: {integrity: sha512-0X2CImDkJGApiAlcf0ODKIneSwBPhqJawOa5wCtKbu7ZECrmS26NvtSILynQ66cgkT/RJ4LidJOc3bUESwmU8g==}
    engines: {node: '>=0.10.0'}

  reactcss@1.2.3:
    resolution: {integrity: sha512-KiwVUcFu1RErkI97ywr8nvx8dNOpT03rbnma0SSalTYjkrPYaEajR4a/MRt6DZ46K6arDRbWMNHF+xH7G7n/8A==}
    peerDependencies:
      react: '*'

  readable-stream@2.3.8:
    resolution: {integrity: sha512-8p0AUk4XODgIewSi0l8Epjs+EVnWiK7NoDIEGU0HhE7+ZyY8D1IMY7odu5lRrFXGg71L15KG8QrPmum45RTtdA==}

  readable-stream@3.6.2:
    resolution: {integrity: sha512-9u/sniCrY3D5WdsERHzHE4G2YCXqoG5FTHUiCC4SIbr6XcLZBY05ya9EKjYek9O5xOAwjGq+1JdGBAS7Q9ScoA==}
    engines: {node: '>= 6'}

  readdir-glob@1.1.3:
    resolution: {integrity: sha512-v05I2k7xN8zXvPD9N+z/uhXPaj0sUFCe2rcWZIpBsqxfP7xXFQ0tipAd/wjj1YxWyWtUS5IDJpOG82JKt2EAVA==}

  redux@4.2.1:
    resolution: {integrity: sha512-LAUYz4lc+Do8/g7aeRa8JkyDErK6ekstQaqWQrNRW//MY1TvCEpMtpTWvlQ+FPbWCx+Xixu/6SHt5N0HR+SB4w==}

  remove-accents@0.5.0:
    resolution: {integrity: sha512-8g3/Otx1eJaVD12e31UbJj1YzdtVvzH85HV7t+9MJYk/u3XmkOUJ5Ys9wQrf9PCPK8+xn4ymzqYCiZl6QWKn+A==}

  resize-observer-polyfill@1.5.1:
    resolution: {integrity: sha512-LwZrotdHOo12nQuZlHEmtuXdqGoOD0OhaxopaNFxWzInpEgaLWoVuAMbTzixuosCx2nEG58ngzW3vxdWoxIgdg==}

  rimraf@2.7.1:
    resolution: {integrity: sha512-uWjbaKIK3T1OSVptzX7Nl6PvQ3qAGtKEtVRjRuazjfL3Bx5eI409VZSqgND+4UNnmzLVdPj9FqFJNPqBZFve4w==}
    deprecated: Rimraf versions prior to v4 are no longer supported
    hasBin: true

  rimraf@3.0.2:
    resolution: {integrity: sha512-JZkJMZkAGFFPP2YqXZXPbMlMBgsxzE8ILs4lMIX/2o0L9UBw9O/Y3o6wFw/i9YLapcUJWwqbi3kdxIPdC62TIA==}
    deprecated: Rimraf versions prior to v4 are no longer supported
    hasBin: true

  rollup@4.50.0:
    resolution: {integrity: sha512-/Zl4D8zPifNmyGzJS+3kVoyXeDeT/GrsJM94sACNg9RtUE0hrHa1bNPtRSrfHTMH5HjRzce6K7rlTh3Khiw+pw==}
    engines: {node: '>=18.0.0', npm: '>=8.0.0'}
    hasBin: true

  safe-buffer@5.1.2:
    resolution: {integrity: sha512-Gd2UZBJDkXlY7GbJxfsE8/nvKkUEU1G38c1siN6QP6a9PT9MmHB8GnpscSmMJSoF8LOIrt8ud/wPtojys4G6+g==}

  safe-buffer@5.2.1:
    resolution: {integrity: sha512-rp3So07KcdmmKbGvgaNxQSJr7bGVSVk5S9Eq1F+ppbRo70+YeaDxkw5Dd8NPN+GD6bjnYm2VuPuCXmpuYvmCXQ==}

  saxes@5.0.1:
    resolution: {integrity: sha512-5LBh1Tls8c9xgGjw3QrMwETmTMVk0oFgvrFSvWx62llR2hcEInrKNZ2GZCCuuy2lvWrdl5jhbpeqc5hRYKFOcw==}
    engines: {node: '>=10'}

  scheduler@0.20.2:
    resolution: {integrity: sha512-2eWfGgAqqWFGqtdMmcL5zCMK1U8KlXv8SQFGglL3CEtd0aDVDWgeF/YoCmvln55m5zSk3J/20hTaSBeSObsQDQ==}

  semver@6.3.1:
    resolution: {integrity: sha512-BR7VvDCVHO+q2xBEWskxS6DJE1qRnb7DxzUrogb71CWoSficBxYsiAGd+Kl0mmq/MprG9yArRkyrQxTO6XjMzA==}
    hasBin: true

  semver@7.7.2:
    resolution: {integrity: sha512-RF0Fw+rO5AMf9MAyaRXI4AV0Ulj5lMHqVxxdSgiVbixSCXoEmmX/jk0CuJw4+3SqroYO9VoUh+HcuJivvtJemA==}
    engines: {node: '>=10'}
    hasBin: true

  set-blocking@2.0.0:
    resolution: {integrity: sha512-KiKBS8AnWGEyLzofFfmvKwpdPzqiy16LvQfK3yv/fVH7Bj13/wl3JSR1J+rfgRE9q7xUJK4qvgS8raSOeLUehw==}

  setimmediate@1.0.5:
    resolution: {integrity: sha512-MATJdZp8sLqDl/68LfQmbP8zKPLQNV6BIZoIgrscFDQ+RsvK/BxeDQOgyxKKoh0y/8h3BqVFnCqQ/gd+reiIXA==}

  shebang-command@2.0.0:
    resolution: {integrity: sha512-kHxr2zZpYtdmrN1qDjrrX/Z1rR1kG8Dx+gkpK1G4eXmvXswmcE1hTWBWYUzlraYw1/yZp6YuDY77YtvbN0dmDA==}
    engines: {node: '>=8'}

  shebang-regex@3.0.0:
    resolution: {integrity: sha512-7++dFhtcx3353uBaq8DDR4NuxBetBzC7ZQOhmTQInHEd6bSrXdiEyzCvG07Z44UYdLShWUyXt5M/yhz8ekcb1A==}
    engines: {node: '>=8'}

  signal-exit@3.0.7:
    resolution: {integrity: sha512-wnD2ZE+l+SPC/uoS0vXeE9L1+0wuaMqKlfz9AMUo38JsyLSBWSFcHR1Rri62LZc12vLr1gb3jl7iwQhgwpAbGQ==}

  simple-concat@1.0.1:
    resolution: {integrity: sha512-cSFtAPtRhljv69IK0hTVZQ+OfE9nePi/rtJmw5UjHeVyVroEqJXP1sFztKUy1qU+xvz3u/sfYJLa947b7nAN2Q==}

  simple-get@3.1.1:
    resolution: {integrity: sha512-CQ5LTKGfCpvE1K0n2us+kuMPbk/q0EKl82s4aheV9oXjFEz6W/Y7oQFVJuU6QG77hRT4Ghb5RURteF5vnWjupA==}

  smooth-signature@1.1.0:
    resolution: {integrity: sha512-wXBsbWmGxtNPftE7RoCWl5VrFkTT2hxT6/rJHQLOMviVeHB3B38+rh7F41Vd1Zwy9P+568ZDyutN/GWk1VfD+w==}

  sortablejs@1.15.0:
    resolution: {integrity: sha512-bv9qgVMjUMf89wAvM6AxVvS/4MX3sPeN0+agqShejLU5z5GX4C75ow1O2e5k4L6XItUyAK3gH6AxSbXrOM5e8w==}

  sortablejs@1.15.6:
    resolution: {integrity: sha512-aNfiuwMEpfBM/CN6LY0ibyhxPfPbyFeBTYJKCvzkJ2GkUpazIt3H+QIPAMHwqQ7tMKaHz1Qj+rJJCqljnf4p3A==}

  source-map-js@1.2.1:
    resolution: {integrity: sha512-UXWMKhLOwVKb728IUtQPXxfYU+usdybtUrK/8uGE8CQMvrhOpwvzDBwj0QhSL7MQc7vIsISBG8VQ8+IDQxpfQA==}
    engines: {node: '>=0.10.0'}

  ssf@0.11.2:
    resolution: {integrity: sha512-+idbmIXoYET47hH+d7dfm2epdOMUDjqcB4648sTZ+t2JwoyBFL/insLfB/racrDmsKB3diwsDA696pZMieAC5g==}
    engines: {node: '>=0.8'}

  string-width@4.2.3:
    resolution: {integrity: sha512-wKyQRQpjJ0sIp62ErSZdGsjMJWsap5oRNihHhu6G7JVO/9jIB6UyevL+tXuOqrng8j/cxKTWyWUwvSTriiZz/g==}
    engines: {node: '>=8'}

  string_decoder@1.1.1:
    resolution: {integrity: sha512-n/ShnvDi6FHbbVfviro+WojiFzv+s8MPMHBczVePfUpDJLwoLT0ht1l4YwBCbi8pJAveEEdnkHyPyTP/mzRfwg==}

  string_decoder@1.3.0:
    resolution: {integrity: sha512-hkRX8U1WjJFd8LsDJ2yQ/wWWxaopEsABU1XfkM8A+j0+85JAGppt16cr1Whg6KIbb4okU6Mql6BOj+uup/wKeA==}

  strip-ansi@6.0.1:
    resolution: {integrity: sha512-Y38VPSHcqkFrCpFnQ9vuSXmquuv5oXOKpGeT6aGrr3o3Gc9AlVa6JBfUSOCnbxGGZF+/0ooI7KrPuUSztUdU5A==}
    engines: {node: '>=8'}

  style-inject@0.3.0:
    resolution: {integrity: sha512-IezA2qp+vcdlhJaVm5SOdPPTUu0FCEqfNSli2vRuSIBbu5Nq5UvygTk/VzeCqfLz2Atj3dVII5QBKGZRZ0edzw==}

  tar-stream@2.2.0:
    resolution: {integrity: sha512-ujeqbceABgwMZxEJnk2HDY2DlnUZ+9oEcb1KzTVfYHio0UE6dG71n60d8D2I4qNvleWrrXpmjpt7vZeF1LnMZQ==}
    engines: {node: '>=6'}

  tar@6.2.1:
    resolution: {integrity: sha512-DZ4yORTwrbTj/7MZYq2w+/ZFdI6OZ/f9SFHR+71gIVUZhOQPHzVCLpvRnPgyaMpfWxxk/4ONva3GQSyNIKRv6A==}
    engines: {node: '>=10'}

  tiny-invariant@1.3.3:
    resolution: {integrity: sha512-+FbBPE1o9QAYvviau/qC5SE3caw21q3xkvWKBtja5vgqOWIHHJ3ioaq1VPfn/Szqctz2bU/oYeKd9/z5BL+PVg==}

  tinycolor2@1.6.0:
    resolution: {integrity: sha512-XPaBkWQJdsf3pLKJV9p4qN/S+fm2Oj8AIPo1BTUhg5oxkvm9+SVEGFdhyOz7tTdUTfvxMiAs4sp6/eZO2Ew+pw==}

  tinyglobby@0.2.14:
    resolution: {integrity: sha512-tX5e7OM1HnYr2+a2C/4V0htOcSQcoSTH9KgJnVvNm5zm/cyEWKJ7j7YutsH9CxMdtOkkLFy2AHrMci9IM8IPZQ==}
    engines: {node: '>=12.0.0'}

  tinymce@6.8.6:
    resolution: {integrity: sha512-++XYEs8lKWvZxDCjrr8Baiw7KiikraZ5JkLMg6EdnUVNKJui0IsrAADj5MsyUeFkcEryfn2jd3p09H7REvewyg==}

  tmp@0.2.5:
    resolution: {integrity: sha512-voyz6MApa1rQGUxT3E+BK7/ROe8itEx7vD8/HEvt4xwXucvQ5G5oeEiHkmHZJuBO21RpOf+YYm9MOivj709jow==}
    engines: {node: '>=14.14'}

  tr46@0.0.3:
    resolution: {integrity: sha512-N3WMsuqV66lT30CrXNbEjx4GEwlow3v6rr4mCcv6prnfwhS01rkgyFdjPNBYd9br7LpXV1+Emh01fHnq2Gdgrw==}

  traverse@0.3.9:
    resolution: {integrity: sha512-iawgk0hLP3SxGKDfnDJf8wTz4p2qImnyihM5Hh/sGvQ3K37dPi/w8sRhdNIxYA1TwFwc5mDhIJq+O0RsvXBKdQ==}

  tslib@2.3.0:
    resolution: {integrity: sha512-N82ooyxVNm6h1riLCoyS9e3fuJ3AMG2zIZs2Gd1ATcSFjSA23Q0fzjjZeh0jbJvWVDZ0cJT8yaNNaaXHzueNjg==}

  tslib@2.8.1:
    resolution: {integrity: sha512-oJFu94HQb+KVduSUQL7wnpmqnfmLsOA/nAh6b6EH0wCEoK0/mPeXU6c3wKDV83MkOuHPRHtSXKKU99IBazS/2w==}

  ua-parser-js@1.0.41:
    resolution: {integrity: sha512-LbBDqdIC5s8iROCUjMbW1f5dJQTEFB1+KO9ogbvlb3nm9n4YHa5p4KTvFPWvh2Hs8gZMBuiB1/8+pdfe/tDPug==}
    hasBin: true

  uc.micro@1.0.6:
    resolution: {integrity: sha512-8Y75pvTYkLJW2hWQHXxoqRgV7qb9B+9vFEtidML+7koHUFapnVJAZ6cKs+Qjz5Aw3aZWHMC6u0wJE3At+nSGwA==}

  uncontrollable@7.2.1:
    resolution: {integrity: sha512-svtcfoTADIB0nT9nltgjujTi7BzVmwjZClOmskKu/E8FW9BXzg9os8OLr4f8Dlnk0rYWJIWr4wv9eKUXiQvQwQ==}
    peerDependencies:
      react: '>=15.0.0'

  unzipper@0.10.14:
    resolution: {integrity: sha512-ti4wZj+0bQTiX2KmKWuwj7lhV+2n//uXEotUmGuQqrbVZSEGFMbI68+c6JCQ8aAmUWYvtHEz2A8K6wXvueR/6g==}

  use-composed-ref@1.4.0:
    resolution: {integrity: sha512-djviaxuOOh7wkj0paeO1Q/4wMZ8Zrnag5H6yBvzN7AKKe8beOaED9SF5/ByLqsku8NP4zQqsvM2u3ew/tJK8/w==}
    peerDependencies:
      '@types/react': '*'
      react: ^16.8.0 || ^17.0.0 || ^18.0.0 || ^19.0.0
    peerDependenciesMeta:
      '@types/react':
        optional: true

  use-isomorphic-layout-effect@1.2.1:
    resolution: {integrity: sha512-tpZZ+EX0gaghDAiFR37hj5MgY6ZN55kLiPkJsKxBMZ6GZdOSPJXiOzPM984oPYZ5AnehYx5WQp1+ME8I/P/pRA==}
    peerDependencies:
      '@types/react': '*'
      react: ^16.8.0 || ^17.0.0 || ^18.0.0 || ^19.0.0
    peerDependenciesMeta:
      '@types/react':
        optional: true

  use-latest@1.3.0:
    resolution: {integrity: sha512-mhg3xdm9NaM8q+gLT8KryJPnRFOz1/5XPBhmDEVZK1webPzDjrPk7f/mbpeLqTgB9msytYWANxgALOCJKnLvcQ==}
    peerDependencies:
      '@types/react': '*'
      react: ^16.8.0 || ^17.0.0 || ^18.0.0 || ^19.0.0
    peerDependenciesMeta:
      '@types/react':
        optional: true

  util-deprecate@1.0.2:
    resolution: {integrity: sha512-EPD5q1uXyFxJpCrLnCc1nHnq3gOa6DZBocAIiI2TaSCA7VCJ1UJDMagCzIkXNsUYfD1daK//LTEQ8xiIbrHtcw==}

  uuid@8.3.2:
    resolution: {integrity: sha512-+NYs2QeMWy+GWFOEm9xnn6HCDp0l7QBD7ml8zLUmJ+93Q5NF0NocErnwkTkXVFNiX3/fpC6afS8Dhb/gz7R7eg==}
    hasBin: true

  video-react@0.15.0:
    resolution: {integrity: sha512-wF3BwG1qikkSX11nu0KsygxeWehzMaYpd4Uvy1sKLFnCNk794f9TPL4q/+mMmLJ8uYb5DSlgg6VraTHyihiMHQ==}
    peerDependencies:
      react: ^15.0.0 || ^16.0.0 || ^17.0.0
      react-dom: ^15.0.0 || ^16.0.0 || ^17.0.0

  vite@7.1.4:
    resolution: {integrity: sha512-X5QFK4SGynAeeIt+A7ZWnApdUyHYm+pzv/8/A57LqSGcI88U6R6ipOs3uCesdc6yl7nl+zNO0t8LmqAdXcQihw==}
    engines: {node: ^20.19.0 || >=22.12.0}
    hasBin: true
    peerDependencies:
      '@types/node': ^20.19.0 || >=22.12.0
      jiti: '>=1.21.0'
      less: ^4.0.0
      lightningcss: ^1.21.0
      sass: ^1.70.0
      sass-embedded: ^1.70.0
      stylus: '>=0.54.8'
      sugarss: ^5.0.0
      terser: ^5.16.0
      tsx: ^4.8.1
      yaml: ^2.4.2
    peerDependenciesMeta:
      '@types/node':
        optional: true
      jiti:
        optional: true
      less:
        optional: true
      lightningcss:
        optional: true
      sass:
        optional: true
      sass-embedded:
        optional: true
      stylus:
        optional: true
      sugarss:
        optional: true
      terser:
        optional: true
      tsx:
        optional: true
      yaml:
        optional: true

  vue-router@4.5.1:
    resolution: {integrity: sha512-ogAF3P97NPm8fJsE4by9dwSYtDwXIY1nFY9T6DyQnGHd1E2Da94w9JIolpe42LJGIl0DwOHBi8TcRPlPGwbTtw==}
    peerDependencies:
      vue: ^3.2.0

  vue@3.5.21:
    resolution: {integrity: sha512-xxf9rum9KtOdwdRkiApWL+9hZEMWE90FHh8yS1+KJAiWYh+iGWV1FquPjoO9VUHQ+VIhsCXNNyZ5Sf4++RVZBA==}
    peerDependencies:
      typescript: '*'
    peerDependenciesMeta:
      typescript:
        optional: true

  warning@4.0.3:
    resolution: {integrity: sha512-rpJyN222KWIvHJ/F53XSZv0Zl/accqHR8et1kpaMTD/fLCRxtV8iX8czMzY7sVZupTI3zcUTg8eycS2kNF9l6w==}

  webidl-conversions@3.0.1:
    resolution: {integrity: sha512-2JAn3z8AR6rjK8Sm8orRC0h/bcl/DqL7tRPdGZ4I1CjdF+EaMLmYxBHyXuKL849eucPFhvBoxMsflfOb8kxaeQ==}

  webworkify-webpack@https://codeload.github.com/xqq/webworkify-webpack/tar.gz/24d1e719b4a6cac37a518b2bb10fe124527ef4ef:
    resolution: {tarball: https://codeload.github.com/xqq/webworkify-webpack/tar.gz/24d1e719b4a6cac37a518b2bb10fe124527ef4ef}
    version: 2.1.5

  whatwg-url@5.0.0:
    resolution: {integrity: sha512-saE57nupxk6v3HY35+jzBwYa0rKSy0XR8JSxZPwgLr7ys0IBzhGviA1/TUGJLmSVqs8pb9AnvICXEuOHLprYTw==}

  which@2.0.2:
    resolution: {integrity: sha512-BLI3Tl1TW3Pvl70l3yq3Y64i+awpwXqsGBYWkkqMtnbXgrMD+yj7rhW0kuEDxzJaYXGjEW5ogapKNMEKNMjibA==}
    engines: {node: '>= 8'}
    hasBin: true

  wide-align@1.1.5:
    resolution: {integrity: sha512-eDMORYaPNZ4sQIuuYPDHdQvf4gyCF9rEEV/yPxGfwPkRodwEgiMUUXTx/dex+Me0wxx53S+NgUHaP7y3MGlDmg==}

  wmf@1.0.2:
    resolution: {integrity: sha512-/p9K7bEh0Dj6WbXg4JG0xvLQmIadrner1bi45VMJTfnbVHsc7yIajZyoSoK60/dtVBs12Fm6WkUI5/3WAVsNMw==}
    engines: {node: '>=0.8'}

  word@0.3.0:
    resolution: {integrity: sha512-OELeY0Q61OXpdUfTp+oweA/vtLVg5VDOXh+3he3PNzLGG/y0oylSOC1xRVj0+l4vQ3tj/bB1HVHv1ocXkQceFA==}
    engines: {node: '>=0.8'}

  wrappy@1.0.2:
    resolution: {integrity: sha512-l4Sp/DRseor9wL6EvV2+TuQn63dMkPjZ/sp9XkghTEbV9KlPS1xUsZ3u7/IQO4wxtcFB4bgpQPRcR3QCvezPcQ==}

  xlsx@0.18.5:
    resolution: {integrity: sha512-dmg3LCjBPHZnQp5/F/+nnTa+miPJxUXB6vtk42YjBBKayDNagxGEeIdWApkYPOf3Z3pm3k62Knjzp7lMeTEtFQ==}
    engines: {node: '>=0.8'}
    hasBin: true

  xmlchars@2.2.0:
    resolution: {integrity: sha512-JZnDKK8B0RCDw84FNdDAIpZK+JuJw+s7Lz8nksI7SIuU3UXJJslUthsi+uWBUYOwPFwW7W7PRLRfUKpxjtjFCw==}

  yallist@4.0.0:
    resolution: {integrity: sha512-3wdGidZyq5PB084XLES5TpOSRA3wjXAlIWMhum2kRcv/41Sn2emQ0dycQW4uZXLejwKvg6EsvbdlVL+FYEct7A==}

  zip-stream@4.1.1:
    resolution: {integrity: sha512-9qv4rlDiopXg4E69k+vMHjNN63YFMe9sZMrdlvKnCjlCRWeCBswPPMPUfx+ipsAWq1LXHe70RcbaHdJJpS6hyQ==}
    engines: {node: '>= 10'}

  zrender@5.6.0:
    resolution: {integrity: sha512-uzgraf4njmmHAbEUxMJ8Oxg+P3fT04O+9p7gY+wJRVxo8Ge+KmYv0WJev945EH4wFuc4OY2NLXz46FZrWS9xJg==}

snapshots:

  '@babel/helper-string-parser@7.27.1': {}

  '@babel/helper-validator-identifier@7.27.1': {}

  '@babel/parser@7.28.3':
    dependencies:
      '@babel/types': 7.28.2

  '@babel/runtime@7.28.4': {}

  '@babel/types@7.28.2':
    dependencies:
      '@babel/helper-string-parser': 7.27.1
      '@babel/helper-validator-identifier': 7.27.1

  '@esbuild/aix-ppc64@0.25.9':
    optional: true

  '@esbuild/android-arm64@0.25.9':
    optional: true

  '@esbuild/android-arm@0.25.9':
    optional: true

  '@esbuild/android-x64@0.25.9':
    optional: true

  '@esbuild/darwin-arm64@0.25.9':
    optional: true

  '@esbuild/darwin-x64@0.25.9':
    optional: true

  '@esbuild/freebsd-arm64@0.25.9':
    optional: true

  '@esbuild/freebsd-x64@0.25.9':
    optional: true

  '@esbuild/linux-arm64@0.25.9':
    optional: true

  '@esbuild/linux-arm@0.25.9':
    optional: true

  '@esbuild/linux-ia32@0.25.9':
    optional: true

  '@esbuild/linux-loong64@0.25.9':
    optional: true

  '@esbuild/linux-mips64el@0.25.9':
    optional: true

  '@esbuild/linux-ppc64@0.25.9':
    optional: true

  '@esbuild/linux-riscv64@0.25.9':
    optional: true

  '@esbuild/linux-s390x@0.25.9':
    optional: true

  '@esbuild/linux-x64@0.25.9':
    optional: true

  '@esbuild/netbsd-arm64@0.25.9':
    optional: true

  '@esbuild/netbsd-x64@0.25.9':
    optional: true

  '@esbuild/openbsd-arm64@0.25.9':
    optional: true

  '@esbuild/openbsd-x64@0.25.9':
    optional: true

  '@esbuild/openharmony-arm64@0.25.9':
    optional: true

  '@esbuild/sunos-x64@0.25.9':
    optional: true

  '@esbuild/win32-arm64@0.25.9':
    optional: true

  '@esbuild/win32-ia32@0.25.9':
    optional: true

  '@esbuild/win32-x64@0.25.9':
    optional: true

  '@fast-csv/format@4.3.5':
    dependencies:
      '@types/node': 14.18.63
      lodash.escaperegexp: 4.1.2
      lodash.isboolean: 3.0.3
      lodash.isequal: 4.5.0
      lodash.isfunction: 3.0.9
      lodash.isnil: 4.0.0

  '@fast-csv/parse@4.3.6':
    dependencies:
      '@types/node': 14.18.63
      lodash.escaperegexp: 4.1.2
      lodash.groupby: 4.6.0
      lodash.isfunction: 3.0.9
      lodash.isnil: 4.0.0
      lodash.isundefined: 3.0.1
      lodash.uniq: 4.5.0

  '@icons/material@0.2.4(react@16.14.0)':
    dependencies:
      react: 16.14.0

  '@jridgewell/sourcemap-codec@1.5.5': {}

  '@mapbox/node-pre-gyp@1.0.11':
    dependencies:
      detect-libc: 2.0.4
      https-proxy-agent: 5.0.1
      make-dir: 3.1.0
      node-fetch: 2.7.0
      nopt: 5.0.0
      npmlog: 5.0.1
      rimraf: 3.0.2
      semver: 7.7.2
      tar: 6.2.1
    transitivePeerDependencies:
      - encoding
      - supports-color
    optional: true

  '@popperjs/core@2.11.8': {}

  '@rc-component/mini-decimal@1.1.0':
    dependencies:
      '@babel/runtime': 7.28.4

  '@rc-component/portal@1.1.2(react-dom@17.0.2(react@16.14.0))(react@16.14.0)':
    dependencies:
      '@babel/runtime': 7.28.4
      classnames: 2.3.2
      rc-util: 5.44.4(react-dom@17.0.2(react@16.14.0))(react@16.14.0)
      react: 16.14.0
      react-dom: 17.0.2(react@16.14.0)

  '@rc-component/trigger@2.3.0(react-dom@17.0.2(react@16.14.0))(react@16.14.0)':
    dependencies:
      '@babel/runtime': 7.28.4
      '@rc-component/portal': 1.1.2(react-dom@17.0.2(react@16.14.0))(react@16.14.0)
      classnames: 2.3.2
      rc-motion: 2.9.5(react-dom@17.0.2(react@16.14.0))(react@16.14.0)
      rc-resize-observer: 1.4.3(react-dom@17.0.2(react@16.14.0))(react@16.14.0)
      rc-util: 5.44.4(react-dom@17.0.2(react@16.14.0))(react@16.14.0)
      react: 16.14.0
      react-dom: 17.0.2(react@16.14.0)

  '@restart/hooks@0.3.27(react@16.14.0)':
    dependencies:
      dequal: 2.0.3
      react: 16.14.0

  '@rolldown/pluginutils@1.0.0-beta.29': {}

  '@rollup/rollup-android-arm-eabi@4.50.0':
    optional: true

  '@rollup/rollup-android-arm64@4.50.0':
    optional: true

  '@rollup/rollup-darwin-arm64@4.50.0':
    optional: true

  '@rollup/rollup-darwin-x64@4.50.0':
    optional: true

  '@rollup/rollup-freebsd-arm64@4.50.0':
    optional: true

  '@rollup/rollup-freebsd-x64@4.50.0':
    optional: true

  '@rollup/rollup-linux-arm-gnueabihf@4.50.0':
    optional: true

  '@rollup/rollup-linux-arm-musleabihf@4.50.0':
    optional: true

  '@rollup/rollup-linux-arm64-gnu@4.50.0':
    optional: true

  '@rollup/rollup-linux-arm64-musl@4.50.0':
    optional: true

  '@rollup/rollup-linux-loongarch64-gnu@4.50.0':
    optional: true

  '@rollup/rollup-linux-ppc64-gnu@4.50.0':
    optional: true

  '@rollup/rollup-linux-riscv64-gnu@4.50.0':
    optional: true

  '@rollup/rollup-linux-riscv64-musl@4.50.0':
    optional: true

  '@rollup/rollup-linux-s390x-gnu@4.50.0':
    optional: true

  '@rollup/rollup-linux-x64-gnu@4.50.0':
    optional: true

  '@rollup/rollup-linux-x64-musl@4.50.0':
    optional: true

  '@rollup/rollup-openharmony-arm64@4.50.0':
    optional: true

  '@rollup/rollup-win32-arm64-msvc@4.50.0':
    optional: true

  '@rollup/rollup-win32-ia32-msvc@4.50.0':
    optional: true

  '@rollup/rollup-win32-x64-msvc@4.50.0':
    optional: true

  '@types/estree@1.0.8': {}

  '@types/node@14.18.63': {}

  '@types/react@19.1.12':
    dependencies:
      csstype: 3.1.3

  '@types/warning@3.0.3': {}

  '@vitejs/plugin-vue@6.0.1(vite@7.1.4)(vue@3.5.21)':
    dependencies:
      '@rolldown/pluginutils': 1.0.0-beta.29
      vite: 7.1.4
      vue: 3.5.21

  '@vue/compiler-core@3.5.21':
    dependencies:
      '@babel/parser': 7.28.3
      '@vue/shared': 3.5.21
      entities: 4.5.0
      estree-walker: 2.0.2
      source-map-js: 1.2.1

  '@vue/compiler-dom@3.5.21':
    dependencies:
      '@vue/compiler-core': 3.5.21
      '@vue/shared': 3.5.21

  '@vue/compiler-sfc@3.5.21':
    dependencies:
      '@babel/parser': 7.28.3
      '@vue/compiler-core': 3.5.21
      '@vue/compiler-dom': 3.5.21
      '@vue/compiler-ssr': 3.5.21
      '@vue/shared': 3.5.21
      estree-walker: 2.0.2
      magic-string: 0.30.18
      postcss: 8.5.6
      source-map-js: 1.2.1

  '@vue/compiler-ssr@3.5.21':
    dependencies:
      '@vue/compiler-dom': 3.5.21
      '@vue/shared': 3.5.21

  '@vue/devtools-api@6.6.4': {}

  '@vue/reactivity@3.5.21':
    dependencies:
      '@vue/shared': 3.5.21

  '@vue/runtime-core@3.5.21':
    dependencies:
      '@vue/reactivity': 3.5.21
      '@vue/shared': 3.5.21

  '@vue/runtime-dom@3.5.21':
    dependencies:
      '@vue/reactivity': 3.5.21
      '@vue/runtime-core': 3.5.21
      '@vue/shared': 3.5.21
      csstype: 3.1.3

  '@vue/server-renderer@3.5.21(vue@3.5.21)':
    dependencies:
      '@vue/compiler-ssr': 3.5.21
      '@vue/shared': 3.5.21
      vue: 3.5.21

  '@vue/shared@3.5.21': {}

  '@webcomponents/webcomponentsjs@2.8.0': {}

  abbrev@1.1.1:
    optional: true

  adler-32@1.3.1: {}

  agent-base@6.0.2:
    dependencies:
      debug: 4.4.1
    transitivePeerDependencies:
      - supports-color
    optional: true

  amis-core@6.13.0(@types/react@19.1.12)(amis-formula@6.13.0)(react-dom@17.0.2(react@16.14.0))(react-is@18.3.1)(react@16.14.0):
    dependencies:
      '@rc-component/mini-decimal': 1.1.0
      amis-formula: 6.13.0
      classnames: 2.3.2
      cross-env: 7.0.3
      file-saver: 2.0.5
      hoist-non-react-statics: 3.3.2
      lodash: 4.17.21
      match-sorter: 6.3.4
      mobx: 4.15.7
      mobx-react: 6.3.1(mobx@4.15.7)(react-dom@17.0.2(react@16.14.0))(react@16.14.0)
      mobx-state-tree: 3.17.3(mobx@4.15.7)
      moment: 2.30.1
      papaparse: 5.5.3
      path-to-regexp: 6.2.0
      qs: 6.9.7
      react: 16.14.0
      react-dom: 17.0.2(react@16.14.0)
      react-intersection-observer: 9.5.2(react@16.14.0)
      react-is: 18.3.1
      react-json-view: 1.21.3(@types/react@19.1.12)(react-dom@17.0.2(react@16.14.0))(react@16.14.0)
      react-overlays: 5.1.1(react-dom@17.0.2(react@16.14.0))(react@16.14.0)
      tslib: 2.8.1
      uncontrollable: 7.2.1(react@16.14.0)
    transitivePeerDependencies:
      - '@types/react'
      - encoding
      - react-native

  amis-editor-core@6.13.0(d219b3d8b12491cdb51b6199147cb0f6):
    dependencies:
      amis: 6.13.0(@types/react@19.1.12)(amis-core@6.13.0(@types/react@19.1.12)(amis-formula@6.13.0)(react-dom@17.0.2(react@16.14.0))(react-is@18.3.1)(react@16.14.0))(amis-ui@6.13.0(@types/react@19.1.12)(amis-core@6.13.0(@types/react@19.1.12)(amis-formula@6.13.0)(react-dom@17.0.2(react@16.14.0))(react-is@18.3.1)(react@16.14.0))(amis-formula@6.13.0)(react-dom@17.0.2(react@16.14.0))(react@16.14.0))(office-viewer@0.3.14(echarts@5.5.1))(react-dom@17.0.2(react@16.14.0))(react@16.14.0)
      amis-core: 6.13.0(@types/react@19.1.12)(amis-formula@6.13.0)(react-dom@17.0.2(react@16.14.0))(react-is@18.3.1)(react@16.14.0)
      amis-formula: 6.13.0
      amis-theme-editor-helper: 2.0.27(ce267645fa719d6855bb651a0571a0bb)
      amis-ui: 6.13.0(@types/react@19.1.12)(amis-core@6.13.0(@types/react@19.1.12)(amis-formula@6.13.0)(react-dom@17.0.2(react@16.14.0))(react-is@18.3.1)(react@16.14.0))(amis-formula@6.13.0)(react-dom@17.0.2(react@16.14.0))(react@16.14.0)
      axios: 0.21.1
      deep-diff: 1.0.2
      i18n-runtime: 1.0.10(amis-core@6.13.0(@types/react@19.1.12)(amis-formula@6.13.0)(react-dom@17.0.2(react@16.14.0))(react-is@18.3.1)(react@16.14.0))
      json-ast-comments: 1.1.1
      lodash: 4.17.21
      mobx: 4.15.7
      mobx-react: 6.3.1(mobx@4.15.7)(react-dom@17.0.2(react@16.14.0))(react@16.14.0)
      mobx-state-tree: 3.17.3(mobx@4.15.7)
      react: 16.14.0
      react-dom: 17.0.2(react@16.14.0)
      react-frame-component: 5.2.7(prop-types@15.8.1)(react-dom@17.0.2(react@16.14.0))(react@16.14.0)
      react-json-view: 1.21.3(@types/react@19.1.12)(react-dom@17.0.2(react@16.14.0))(react@16.14.0)
      sortablejs: 1.15.6
    transitivePeerDependencies:
      - '@types/react'
      - debug
      - encoding
      - prop-types
      - react-native

  amis-editor@6.13.0(714e4a24ab16266dd62b4510cbf1985d):
    dependencies:
      '@webcomponents/webcomponentsjs': 2.8.0
      amis: 6.13.0(@types/react@19.1.12)(amis-core@6.13.0(@types/react@19.1.12)(amis-formula@6.13.0)(react-dom@17.0.2(react@16.14.0))(react-is@18.3.1)(react@16.14.0))(amis-ui@6.13.0(@types/react@19.1.12)(amis-core@6.13.0(@types/react@19.1.12)(amis-formula@6.13.0)(react-dom@17.0.2(react@16.14.0))(react-is@18.3.1)(react@16.14.0))(amis-formula@6.13.0)(react-dom@17.0.2(react@16.14.0))(react@16.14.0))(office-viewer@0.3.14(echarts@5.5.1))(react-dom@17.0.2(react@16.14.0))(react@16.14.0)
      amis-core: 6.13.0(@types/react@19.1.12)(amis-formula@6.13.0)(react-dom@17.0.2(react@16.14.0))(react-is@18.3.1)(react@16.14.0)
      amis-editor-core: 6.13.0(d219b3d8b12491cdb51b6199147cb0f6)
      amis-formula: 6.13.0
      amis-postcss: 1.0.0
      amis-theme-editor-helper: 2.0.27(ce267645fa719d6855bb651a0571a0bb)
      amis-ui: 6.13.0(@types/react@19.1.12)(amis-core@6.13.0(@types/react@19.1.12)(amis-formula@6.13.0)(react-dom@17.0.2(react@16.14.0))(react-is@18.3.1)(react@16.14.0))(amis-formula@6.13.0)(react-dom@17.0.2(react@16.14.0))(react@16.14.0)
      i18n-runtime: 1.0.10(amis-core@6.13.0(@types/react@19.1.12)(amis-formula@6.13.0)(react-dom@17.0.2(react@16.14.0))(react-is@18.3.1)(react@16.14.0))
      lodash: 4.17.21
      mobx-state-tree: 3.17.3(mobx@4.15.7)
      react: 16.14.0
      react-dom: 17.0.2(react@16.14.0)
    transitivePeerDependencies:
      - '@types/react'
      - debug
      - encoding
      - mobx
      - prop-types
      - react-native

  amis-formula@6.13.0:
    dependencies:
      lodash: 4.17.21
      moment: 2.30.1
      tslib: 2.8.1

  amis-postcss@1.0.0: {}

  amis-theme-editor-helper@2.0.27(ce267645fa719d6855bb651a0571a0bb):
    dependencies:
      amis: 6.13.0(@types/react@19.1.12)(amis-core@6.13.0(@types/react@19.1.12)(amis-formula@6.13.0)(react-dom@17.0.2(react@16.14.0))(react-is@18.3.1)(react@16.14.0))(amis-ui@6.13.0(@types/react@19.1.12)(amis-core@6.13.0(@types/react@19.1.12)(amis-formula@6.13.0)(react-dom@17.0.2(react@16.14.0))(react-is@18.3.1)(react@16.14.0))(amis-formula@6.13.0)(react-dom@17.0.2(react@16.14.0))(react@16.14.0))(office-viewer@0.3.14(echarts@5.5.1))(react-dom@17.0.2(react@16.14.0))(react@16.14.0)
      amis-core: 6.13.0(@types/react@19.1.12)(amis-formula@6.13.0)(react-dom@17.0.2(react@16.14.0))(react-is@18.3.1)(react@16.14.0)
      amis-ui: 6.13.0(@types/react@19.1.12)(amis-core@6.13.0(@types/react@19.1.12)(amis-formula@6.13.0)(react-dom@17.0.2(react@16.14.0))(react-is@18.3.1)(react@16.14.0))(amis-formula@6.13.0)(react-dom@17.0.2(react@16.14.0))(react@16.14.0)
      i18n-runtime: 1.0.10(amis-core@6.13.0(@types/react@19.1.12)(amis-formula@6.13.0)(react-dom@17.0.2(react@16.14.0))(react-is@18.3.1)(react@16.14.0))
      react: 16.14.0
      react-dom: 17.0.2(react@16.14.0)
      style-inject: 0.3.0

  amis-ui@6.13.0(@types/react@19.1.12)(amis-core@6.13.0(@types/react@19.1.12)(amis-formula@6.13.0)(react-dom@17.0.2(react@16.14.0))(react-is@18.3.1)(react@16.14.0))(amis-formula@6.13.0)(react-dom@17.0.2(react@16.14.0))(react@16.14.0):
    dependencies:
      '@rc-component/mini-decimal': 1.1.0
      amis-core: 6.13.0(@types/react@19.1.12)(amis-formula@6.13.0)(react-dom@17.0.2(react@16.14.0))(react-is@18.3.1)(react@16.14.0)
      amis-formula: 6.13.0
      animate.css: 4.1.1
      classnames: 2.3.2
      codemirror: 5.65.20
      downshift: 6.1.12(react@16.14.0)
      echarts: 5.5.1
      froala-editor: 3.1.1
      hoist-non-react-statics: 3.3.2
      jsbarcode: 3.12.1
      keycode: 2.2.1
      lodash: 4.17.21
      markdown-it: 12.3.2
      markdown-it-html5-media: 0.7.1
      match-sorter: 6.3.4
      mobx: 4.15.7
      mobx-react: 6.3.1(mobx@4.15.7)(react-dom@17.0.2(react@16.14.0))(react@16.14.0)
      mobx-state-tree: 3.17.3(mobx@4.15.7)
      moment: 2.30.1
      monaco-editor: 0.30.1
      prop-types: 15.8.1
      rc-input-number: 7.4.2(react-dom@17.0.2(react@16.14.0))(react@16.14.0)
      rc-menu: 9.16.1(react-dom@17.0.2(react@16.14.0))(react@16.14.0)
      rc-progress: 3.4.2(react-dom@17.0.2(react@16.14.0))(react@16.14.0)
      react: 16.14.0
      react-color: 2.19.3(react@16.14.0)
      react-dom: 17.0.2(react@16.14.0)
      react-draggable: 4.5.0(react-dom@17.0.2(react@16.14.0))(react@16.14.0)
      react-hook-form: 7.39.0(react@16.14.0)
      react-intersection-observer: 9.5.2(react@16.14.0)
      react-json-view: 1.21.3(@types/react@19.1.12)(react-dom@17.0.2(react@16.14.0))(react@16.14.0)
      react-overlays: 5.1.1(react-dom@17.0.2(react@16.14.0))(react@16.14.0)
      react-pdf: 9.0.0(@types/react@19.1.12)(react-dom@17.0.2(react@16.14.0))(react@16.14.0)
      react-textarea-autosize: 8.3.3(@types/react@19.1.12)(react@16.14.0)
      react-transition-group: 4.4.2(react-dom@17.0.2(react@16.14.0))(react@16.14.0)
      react-visibility-sensor: 5.1.1(react-dom@17.0.2(react@16.14.0))(react@16.14.0)
      smooth-signature: 1.1.0
      sortablejs: 1.15.0
      tinymce: 6.8.6
      tslib: 2.8.1
      uncontrollable: 7.2.1(react@16.14.0)
      video-react: 0.15.0(react-dom@17.0.2(react@16.14.0))(react@16.14.0)
    transitivePeerDependencies:
      - '@types/react'
      - encoding
      - react-native
      - supports-color

  amis@6.13.0(@types/react@19.1.12)(amis-core@6.13.0(@types/react@19.1.12)(amis-formula@6.13.0)(react-dom@17.0.2(react@16.14.0))(react-is@18.3.1)(react@16.14.0))(amis-ui@6.13.0(@types/react@19.1.12)(amis-core@6.13.0(@types/react@19.1.12)(amis-formula@6.13.0)(react-dom@17.0.2(react@16.14.0))(react-is@18.3.1)(react@16.14.0))(amis-formula@6.13.0)(react-dom@17.0.2(react@16.14.0))(react@16.14.0))(office-viewer@0.3.14(echarts@5.5.1))(react-dom@17.0.2(react@16.14.0))(react@16.14.0):
    dependencies:
      amis-core: 6.13.0(@types/react@19.1.12)(amis-formula@6.13.0)(react-dom@17.0.2(react@16.14.0))(react-is@18.3.1)(react@16.14.0)
      amis-ui: 6.13.0(@types/react@19.1.12)(amis-core@6.13.0(@types/react@19.1.12)(amis-formula@6.13.0)(react-dom@17.0.2(react@16.14.0))(react-is@18.3.1)(react@16.14.0))(amis-formula@6.13.0)(react-dom@17.0.2(react@16.14.0))(react@16.14.0)
      attr-accept: 2.2.2
      blueimp-canvastoblob: 2.1.0
      classnames: 2.3.2
      downshift: 6.1.12(react@16.14.0)
      echarts: 5.5.1
      echarts-stat: 1.2.0
      echarts-wordcloud: 2.1.0(echarts@5.5.1)
      exceljs: 4.4.0
      file-saver: 2.0.5
      file64: 1.0.5
      hls.js: 1.1.3
      hoist-non-react-statics: 3.3.2
      hotkeys-js: 3.13.15
      immutability-helper: 3.1.1
      jsbarcode: 3.12.1
      keycode: 2.2.1
      lodash: 4.17.21
      match-sorter: 6.3.4
      mobx: 4.15.7
      mobx-react: 6.3.1(mobx@4.15.7)(react-dom@17.0.2(react@16.14.0))(react@16.14.0)
      mobx-state-tree: 3.17.3(mobx@4.15.7)
      moment: 2.30.1
      moment-timezone: 0.5.48
      mpegts.js: 1.8.0
      office-viewer: 0.3.14(echarts@5.5.1)
      prop-types: 15.8.1
      qrcode-react-next: 1.0.0(react@16.14.0)
      react: 16.14.0
      react-cropper: 2.3.3(react@16.14.0)
      react-dom: 17.0.2(react@16.14.0)
      react-dropzone: 11.7.1(react@16.14.0)
      react-intersection-observer: 9.5.2(react@16.14.0)
      react-json-view: 1.21.3(@types/react@19.1.12)(react-dom@17.0.2(react@16.14.0))(react@16.14.0)
      react-transition-group: 4.4.2(react-dom@17.0.2(react@16.14.0))(react@16.14.0)
      sortablejs: 1.15.0
      tslib: 2.8.1
      video-react: 0.15.0(react-dom@17.0.2(react@16.14.0))(react@16.14.0)
      xlsx: 0.18.5
    transitivePeerDependencies:
      - '@types/react'
      - encoding
      - react-native

  animate.css@4.1.1: {}

  ansi-regex@5.0.1:
    optional: true

  aproba@2.1.0:
    optional: true

  archiver-utils@2.1.0:
    dependencies:
      glob: 7.2.3
      graceful-fs: 4.2.11
      lazystream: 1.0.1
      lodash.defaults: 4.2.0
      lodash.difference: 4.5.0
      lodash.flatten: 4.4.0
      lodash.isplainobject: 4.0.6
      lodash.union: 4.6.0
      normalize-path: 3.0.0
      readable-stream: 2.3.8

  archiver-utils@3.0.4:
    dependencies:
      glob: 7.2.3
      graceful-fs: 4.2.11
      lazystream: 1.0.1
      lodash.defaults: 4.2.0
      lodash.difference: 4.5.0
      lodash.flatten: 4.4.0
      lodash.isplainobject: 4.0.6
      lodash.union: 4.6.0
      normalize-path: 3.0.0
      readable-stream: 3.6.2

  archiver@5.3.2:
    dependencies:
      archiver-utils: 2.1.0
      async: 3.2.6
      buffer-crc32: 0.2.13
      readable-stream: 3.6.2
      readdir-glob: 1.1.3
      tar-stream: 2.2.0
      zip-stream: 4.1.1

  are-we-there-yet@2.0.0:
    dependencies:
      delegates: 1.0.0
      readable-stream: 3.6.2
    optional: true

  argparse@2.0.1: {}

  asap@2.0.6: {}

  async@3.2.6: {}

  attr-accept@2.2.2: {}

  axios@0.21.1:
    dependencies:
      follow-redirects: 1.15.11
    transitivePeerDependencies:
      - debug

  balanced-match@1.0.2: {}

  base16@1.0.0: {}

  base64-js@1.5.1: {}

  big-integer@1.6.52: {}

  binary@0.3.0:
    dependencies:
      buffers: 0.1.1
      chainsaw: 0.1.0

  bl@4.1.0:
    dependencies:
      buffer: 5.7.1
      inherits: 2.0.4
      readable-stream: 3.6.2

  bluebird@3.4.7: {}

  blueimp-canvastoblob@2.1.0: {}

  brace-expansion@1.1.12:
    dependencies:
      balanced-match: 1.0.2
      concat-map: 0.0.1

  brace-expansion@2.0.2:
    dependencies:
      balanced-match: 1.0.2

  buffer-crc32@0.2.13: {}

  buffer-indexof-polyfill@1.0.2: {}

  buffer@5.7.1:
    dependencies:
      base64-js: 1.5.1
      ieee754: 1.2.1

  buffers@0.1.1: {}

  canvas@2.11.2:
    dependencies:
      '@mapbox/node-pre-gyp': 1.0.11
      nan: 2.23.0
      simple-get: 3.1.1
    transitivePeerDependencies:
      - encoding
      - supports-color
    optional: true

  cfb@1.2.2:
    dependencies:
      adler-32: 1.3.1
      crc-32: 1.2.2

  chainsaw@0.1.0:
    dependencies:
      traverse: 0.3.9

  chownr@2.0.0:
    optional: true

  classnames@2.3.2: {}

  clsx@2.1.1: {}

  codemirror@5.65.20: {}

  codepage@1.15.0: {}

  color-support@1.1.3:
    optional: true

  compress-commons@4.1.2:
    dependencies:
      buffer-crc32: 0.2.13
      crc32-stream: 4.0.3
      normalize-path: 3.0.0
      readable-stream: 3.6.2

  compute-scroll-into-view@1.0.20: {}

  concat-map@0.0.1: {}

  console-control-strings@1.1.0:
    optional: true

  core-util-is@1.0.3: {}

  crc-32@1.2.2: {}

  crc32-stream@4.0.3:
    dependencies:
      crc-32: 1.2.2
      readable-stream: 3.6.2

  cropperjs@1.6.2: {}

  cross-env@7.0.3:
    dependencies:
      cross-spawn: 7.0.6

  cross-fetch@3.2.0:
    dependencies:
      node-fetch: 2.7.0
    transitivePeerDependencies:
      - encoding

  cross-spawn@7.0.6:
    dependencies:
      path-key: 3.1.1
      shebang-command: 2.0.0
      which: 2.0.2

  csstype@3.1.3: {}

  dayjs@1.11.18: {}

  debug@4.4.1:
    dependencies:
      ms: 2.1.3
    optional: true

  decompress-response@4.2.1:
    dependencies:
      mimic-response: 2.1.0
    optional: true

  deep-diff@1.0.2: {}

  delegates@1.0.0:
    optional: true

  dequal@2.0.3: {}

  detect-libc@2.0.4:
    optional: true

  dom-helpers@5.2.1:
    dependencies:
      '@babel/runtime': 7.28.4
      csstype: 3.1.3

  downshift@6.1.12(react@16.14.0):
    dependencies:
      '@babel/runtime': 7.28.4
      compute-scroll-into-view: 1.0.20
      prop-types: 15.8.1
      react: 16.14.0
      react-is: 17.0.2
      tslib: 2.8.1

  duplexer2@0.1.4:
    dependencies:
      readable-stream: 2.3.8

  echarts-stat@1.2.0: {}

  echarts-wordcloud@2.1.0(echarts@5.5.1):
    dependencies:
      echarts: 5.5.1

  echarts@5.5.1:
    dependencies:
      tslib: 2.3.0
      zrender: 5.6.0

  emoji-regex@8.0.0:
    optional: true

  end-of-stream@1.4.5:
    dependencies:
      once: 1.4.0

  entities@2.1.0: {}

  entities@4.5.0: {}

  es6-promise@4.2.8: {}

  esbuild@0.25.9:
    optionalDependencies:
      '@esbuild/aix-ppc64': 0.25.9
      '@esbuild/android-arm': 0.25.9
      '@esbuild/android-arm64': 0.25.9
      '@esbuild/android-x64': 0.25.9
      '@esbuild/darwin-arm64': 0.25.9
      '@esbuild/darwin-x64': 0.25.9
      '@esbuild/freebsd-arm64': 0.25.9
      '@esbuild/freebsd-x64': 0.25.9
      '@esbuild/linux-arm': 0.25.9
      '@esbuild/linux-arm64': 0.25.9
      '@esbuild/linux-ia32': 0.25.9
      '@esbuild/linux-loong64': 0.25.9
      '@esbuild/linux-mips64el': 0.25.9
      '@esbuild/linux-ppc64': 0.25.9
      '@esbuild/linux-riscv64': 0.25.9
      '@esbuild/linux-s390x': 0.25.9
      '@esbuild/linux-x64': 0.25.9
      '@esbuild/netbsd-arm64': 0.25.9
      '@esbuild/netbsd-x64': 0.25.9
      '@esbuild/openbsd-arm64': 0.25.9
      '@esbuild/openbsd-x64': 0.25.9
      '@esbuild/openharmony-arm64': 0.25.9
      '@esbuild/sunos-x64': 0.25.9
      '@esbuild/win32-arm64': 0.25.9
      '@esbuild/win32-ia32': 0.25.9
      '@esbuild/win32-x64': 0.25.9

  estree-walker@2.0.2: {}

  exceljs@4.4.0:
    dependencies:
      archiver: 5.3.2
      dayjs: 1.11.18
      fast-csv: 4.3.6
      jszip: 3.10.1
      readable-stream: 3.6.2
      saxes: 5.0.1
      tmp: 0.2.5
      unzipper: 0.10.14
      uuid: 8.3.2

  fast-csv@4.3.6:
    dependencies:
      '@fast-csv/format': 4.3.5
      '@fast-csv/parse': 4.3.6

  fbemitter@3.0.0:
    dependencies:
      fbjs: 3.0.5
    transitivePeerDependencies:
      - encoding

  fbjs-css-vars@1.0.2: {}

  fbjs@3.0.5:
    dependencies:
      cross-fetch: 3.2.0
      fbjs-css-vars: 1.0.2
      loose-envify: 1.4.0
      object-assign: 4.1.1
      promise: 7.3.1
      setimmediate: 1.0.5
      ua-parser-js: 1.0.41
    transitivePeerDependencies:
      - encoding

  fdir@6.5.0(picomatch@4.0.3):
    optionalDependencies:
      picomatch: 4.0.3

  file-saver@2.0.5: {}

  file-selector@0.4.0:
    dependencies:
      tslib: 2.8.1

  file64@1.0.5: {}

  flux@4.0.4(react@16.14.0):
    dependencies:
      fbemitter: 3.0.0
      fbjs: 3.0.5
      react: 16.14.0
    transitivePeerDependencies:
      - encoding

  follow-redirects@1.15.11: {}

  frac@1.1.2: {}

  froala-editor@3.1.1: {}

  fs-constants@1.0.0: {}

  fs-minipass@2.1.0:
    dependencies:
      minipass: 3.3.6
    optional: true

  fs.realpath@1.0.0: {}

  fsevents@2.3.3:
    optional: true

  fstream@1.0.12:
    dependencies:
      graceful-fs: 4.2.11
      inherits: 2.0.4
      mkdirp: 0.5.6
      rimraf: 2.7.1

  gauge@3.0.2:
    dependencies:
      aproba: 2.1.0
      color-support: 1.1.3
      console-control-strings: 1.1.0
      has-unicode: 2.0.1
      object-assign: 4.1.1
      signal-exit: 3.0.7
      string-width: 4.2.3
      strip-ansi: 6.0.1
      wide-align: 1.1.5
    optional: true

  glob@7.2.3:
    dependencies:
      fs.realpath: 1.0.0
      inflight: 1.0.6
      inherits: 2.0.4
      minimatch: 3.1.2
      once: 1.4.0
      path-is-absolute: 1.0.1

  graceful-fs@4.2.11: {}

  has-unicode@2.0.1:
    optional: true

  hls.js@1.1.3: {}

  hoist-non-react-statics@3.3.2:
    dependencies:
      react-is: 16.13.1

  hotkeys-js@3.13.15: {}

  https-proxy-agent@5.0.1:
    dependencies:
      agent-base: 6.0.2
      debug: 4.4.1
    transitivePeerDependencies:
      - supports-color
    optional: true

  i18n-runtime@1.0.10(amis-core@6.13.0(@types/react@19.1.12)(amis-formula@6.13.0)(react-dom@17.0.2(react@16.14.0))(react-is@18.3.1)(react@16.14.0)):
    dependencies:
      amis-core: 6.13.0(@types/react@19.1.12)(amis-formula@6.13.0)(react-dom@17.0.2(react@16.14.0))(react-is@18.3.1)(react@16.14.0)

  ieee754@1.2.1: {}

  immediate@3.0.6: {}

  immutability-helper@3.1.1: {}

  inflight@1.0.6:
    dependencies:
      once: 1.4.0
      wrappy: 1.0.2

  inherits@2.0.4: {}

  invariant@2.2.4:
    dependencies:
      loose-envify: 1.4.0

  is-fullwidth-code-point@3.0.0:
    optional: true

  isarray@1.0.0: {}

  isexe@2.0.0: {}

  js-tokens@4.0.0: {}

  jsbarcode@3.12.1: {}

  json-ast-comments@1.1.1: {}

  jszip@3.10.1:
    dependencies:
      lie: 3.3.0
      pako: 1.0.11
      readable-stream: 2.3.8
      setimmediate: 1.0.5

  keycode@2.2.1: {}

  lazystream@1.0.1:
    dependencies:
      readable-stream: 2.3.8

  lie@3.3.0:
    dependencies:
      immediate: 3.0.6

  linkify-it@3.0.3:
    dependencies:
      uc.micro: 1.0.6

  listenercount@1.0.1: {}

  lodash-es@4.17.21: {}

  lodash.curry@4.1.1: {}

  lodash.defaults@4.2.0: {}

  lodash.difference@4.5.0: {}

  lodash.escaperegexp@4.1.2: {}

  lodash.flatten@4.4.0: {}

  lodash.flow@3.5.0: {}

  lodash.groupby@4.6.0: {}

  lodash.isboolean@3.0.3: {}

  lodash.isequal@4.5.0: {}

  lodash.isfunction@3.0.9: {}

  lodash.isnil@4.0.0: {}

  lodash.isplainobject@4.0.6: {}

  lodash.isundefined@3.0.1: {}

  lodash.throttle@4.1.1: {}

  lodash.union@4.6.0: {}

  lodash.uniq@4.5.0: {}

  lodash@4.17.21: {}

  loose-envify@1.4.0:
    dependencies:
      js-tokens: 4.0.0

  magic-string@0.30.18:
    dependencies:
      '@jridgewell/sourcemap-codec': 1.5.5

  make-cancellable-promise@1.3.2: {}

  make-dir@3.1.0:
    dependencies:
      semver: 6.3.1
    optional: true

  make-event-props@1.6.2: {}

  markdown-it-html5-media@0.7.1:
    dependencies:
      markdown-it: 12.3.2

  markdown-it@12.3.2:
    dependencies:
      argparse: 2.0.1
      entities: 2.1.0
      linkify-it: 3.0.3
      mdurl: 1.0.1
      uc.micro: 1.0.6

  match-sorter@6.3.4:
    dependencies:
      '@babel/runtime': 7.28.4
      remove-accents: 0.5.0

  material-colors@1.2.6: {}

  mdurl@1.0.1: {}

  merge-refs@1.3.0(@types/react@19.1.12):
    optionalDependencies:
      '@types/react': 19.1.12

  mimic-response@2.1.0:
    optional: true

  minimatch@3.1.2:
    dependencies:
      brace-expansion: 1.1.12

  minimatch@5.1.6:
    dependencies:
      brace-expansion: 2.0.2

  minimist@1.2.8: {}

  minipass@3.3.6:
    dependencies:
      yallist: 4.0.0
    optional: true

  minipass@5.0.0:
    optional: true

  minizlib@2.1.2:
    dependencies:
      minipass: 3.3.6
      yallist: 4.0.0
    optional: true

  mkdirp@0.5.6:
    dependencies:
      minimist: 1.2.8

  mkdirp@1.0.4:
    optional: true

  mobx-react-lite@2.2.2(mobx@4.15.7)(react-dom@17.0.2(react@16.14.0))(react@16.14.0):
    dependencies:
      mobx: 4.15.7
      react: 16.14.0
    optionalDependencies:
      react-dom: 17.0.2(react@16.14.0)

  mobx-react@6.3.1(mobx@4.15.7)(react-dom@17.0.2(react@16.14.0))(react@16.14.0):
    dependencies:
      mobx: 4.15.7
      mobx-react-lite: 2.2.2(mobx@4.15.7)(react-dom@17.0.2(react@16.14.0))(react@16.14.0)
      react: 16.14.0
    transitivePeerDependencies:
      - react-dom
      - react-native

  mobx-state-tree@3.17.3(mobx@4.15.7):
    dependencies:
      mobx: 4.15.7

  mobx@4.15.7: {}

  moment-timezone@0.5.48:
    dependencies:
      moment: 2.30.1

  moment@2.30.1: {}

  monaco-editor@0.30.1: {}

  monaco-editor@0.52.2: {}

  mpegts.js@1.8.0:
    dependencies:
      es6-promise: 4.2.8
      webworkify-webpack: https://codeload.github.com/xqq/webworkify-webpack/tar.gz/24d1e719b4a6cac37a518b2bb10fe124527ef4ef

  ms@2.1.3:
    optional: true

  nan@2.23.0:
    optional: true

  nanoid@3.3.11: {}

  node-fetch@2.7.0:
    dependencies:
      whatwg-url: 5.0.0

  nopt@5.0.0:
    dependencies:
      abbrev: 1.1.1
    optional: true

  normalize-path@3.0.0: {}

  npmlog@5.0.1:
    dependencies:
      are-we-there-yet: 2.0.0
      console-control-strings: 1.1.0
      gauge: 3.0.2
      set-blocking: 2.0.0
    optional: true

  numfmt@2.5.2: {}

  object-assign@4.1.1: {}

  office-viewer@0.3.14(echarts@5.5.1):
    dependencies:
      echarts: 5.5.1
      numfmt: 2.5.2
      papaparse: 5.5.3
      tslib: 2.8.1

  once@1.4.0:
    dependencies:
      wrappy: 1.0.2

  pako@1.0.11: {}

  papaparse@5.5.3: {}

  path-is-absolute@1.0.1: {}

  path-key@3.1.1: {}

  path-to-regexp@6.2.0: {}

  path2d@0.2.2:
    optional: true

  pdfjs-dist@4.3.136:
    optionalDependencies:
      canvas: 2.11.2
      path2d: 0.2.2
    transitivePeerDependencies:
      - encoding
      - supports-color

  picocolors@1.1.1: {}

  picomatch@4.0.3: {}

  postcss@8.5.6:
    dependencies:
      nanoid: 3.3.11
      picocolors: 1.1.1
      source-map-js: 1.2.1

  process-nextick-args@2.0.1: {}

  promise@7.3.1:
    dependencies:
      asap: 2.0.6

  prop-types@15.8.1:
    dependencies:
      loose-envify: 1.4.0
      object-assign: 4.1.1
      react-is: 16.13.1

  pure-color@1.3.0: {}

  qrcode-react-next@1.0.0(react@16.14.0):
    dependencies:
      react: 16.14.0

  qs@6.9.7: {}

  rc-input-number@7.4.2(react-dom@17.0.2(react@16.14.0))(react@16.14.0):
    dependencies:
      '@babel/runtime': 7.28.4
      '@rc-component/mini-decimal': 1.1.0
      classnames: 2.3.2
      rc-util: 5.44.4(react-dom@17.0.2(react@16.14.0))(react@16.14.0)
      react: 16.14.0
      react-dom: 17.0.2(react@16.14.0)

  rc-menu@9.16.1(react-dom@17.0.2(react@16.14.0))(react@16.14.0):
    dependencies:
      '@babel/runtime': 7.28.4
      '@rc-component/trigger': 2.3.0(react-dom@17.0.2(react@16.14.0))(react@16.14.0)
      classnames: 2.3.2
      rc-motion: 2.9.5(react-dom@17.0.2(react@16.14.0))(react@16.14.0)
      rc-overflow: 1.4.1(react-dom@17.0.2(react@16.14.0))(react@16.14.0)
      rc-util: 5.44.4(react-dom@17.0.2(react@16.14.0))(react@16.14.0)
      react: 16.14.0
      react-dom: 17.0.2(react@16.14.0)

  rc-motion@2.9.5(react-dom@17.0.2(react@16.14.0))(react@16.14.0):
    dependencies:
      '@babel/runtime': 7.28.4
      classnames: 2.3.2
      rc-util: 5.44.4(react-dom@17.0.2(react@16.14.0))(react@16.14.0)
      react: 16.14.0
      react-dom: 17.0.2(react@16.14.0)

  rc-overflow@1.4.1(react-dom@17.0.2(react@16.14.0))(react@16.14.0):
    dependencies:
      '@babel/runtime': 7.28.4
      classnames: 2.3.2
      rc-resize-observer: 1.4.3(react-dom@17.0.2(react@16.14.0))(react@16.14.0)
      rc-util: 5.44.4(react-dom@17.0.2(react@16.14.0))(react@16.14.0)
      react: 16.14.0
      react-dom: 17.0.2(react@16.14.0)

  rc-progress@3.4.2(react-dom@17.0.2(react@16.14.0))(react@16.14.0):
    dependencies:
      '@babel/runtime': 7.28.4
      classnames: 2.3.2
      rc-util: 5.44.4(react-dom@17.0.2(react@16.14.0))(react@16.14.0)
      react: 16.14.0
      react-dom: 17.0.2(react@16.14.0)

  rc-resize-observer@1.4.3(react-dom@17.0.2(react@16.14.0))(react@16.14.0):
    dependencies:
      '@babel/runtime': 7.28.4
      classnames: 2.3.2
      rc-util: 5.44.4(react-dom@17.0.2(react@16.14.0))(react@16.14.0)
      react: 16.14.0
      react-dom: 17.0.2(react@16.14.0)
      resize-observer-polyfill: 1.5.1

  rc-util@5.44.4(react-dom@17.0.2(react@16.14.0))(react@16.14.0):
    dependencies:
      '@babel/runtime': 7.28.4
      react: 16.14.0
      react-dom: 17.0.2(react@16.14.0)
      react-is: 18.3.1

  react-base16-styling@0.6.0:
    dependencies:
      base16: 1.0.0
      lodash.curry: 4.1.1
      lodash.flow: 3.5.0
      pure-color: 1.3.0

  react-color@2.19.3(react@16.14.0):
    dependencies:
      '@icons/material': 0.2.4(react@16.14.0)
      lodash: 4.17.21
      lodash-es: 4.17.21
      material-colors: 1.2.6
      prop-types: 15.8.1
      react: 16.14.0
      reactcss: 1.2.3(react@16.14.0)
      tinycolor2: 1.6.0

  react-cropper@2.3.3(react@16.14.0):
    dependencies:
      cropperjs: 1.6.2
      react: 16.14.0

  react-dom@17.0.2(react@16.14.0):
    dependencies:
      loose-envify: 1.4.0
      object-assign: 4.1.1
      react: 16.14.0
      scheduler: 0.20.2

  react-draggable@4.5.0(react-dom@17.0.2(react@16.14.0))(react@16.14.0):
    dependencies:
      clsx: 2.1.1
      prop-types: 15.8.1
      react: 16.14.0
      react-dom: 17.0.2(react@16.14.0)

  react-dropzone@11.7.1(react@16.14.0):
    dependencies:
      attr-accept: 2.2.2
      file-selector: 0.4.0
      prop-types: 15.8.1
      react: 16.14.0

  react-frame-component@5.2.7(prop-types@15.8.1)(react-dom@17.0.2(react@16.14.0))(react@16.14.0):
    dependencies:
      prop-types: 15.8.1
      react: 16.14.0
      react-dom: 17.0.2(react@16.14.0)

  react-hook-form@7.39.0(react@16.14.0):
    dependencies:
      react: 16.14.0

  react-intersection-observer@9.5.2(react@16.14.0):
    dependencies:
      react: 16.14.0

  react-is@16.13.1: {}

  react-is@17.0.2: {}

  react-is@18.3.1: {}

  react-json-view@1.21.3(@types/react@19.1.12)(react-dom@17.0.2(react@16.14.0))(react@16.14.0):
    dependencies:
      flux: 4.0.4(react@16.14.0)
      react: 16.14.0
      react-base16-styling: 0.6.0
      react-dom: 17.0.2(react@16.14.0)
      react-lifecycles-compat: 3.0.4
      react-textarea-autosize: 8.5.9(@types/react@19.1.12)(react@16.14.0)
    transitivePeerDependencies:
      - '@types/react'
      - encoding

  react-lifecycles-compat@3.0.4: {}

  react-overlays@5.1.1(react-dom@17.0.2(react@16.14.0))(react@16.14.0):
    dependencies:
      '@babel/runtime': 7.28.4
      '@popperjs/core': 2.11.8
      '@restart/hooks': 0.3.27(react@16.14.0)
      '@types/warning': 3.0.3
      dom-helpers: 5.2.1
      prop-types: 15.8.1
      react: 16.14.0
      react-dom: 17.0.2(react@16.14.0)
      uncontrollable: 7.2.1(react@16.14.0)
      warning: 4.0.3

  react-pdf@9.0.0(@types/react@19.1.12)(react-dom@17.0.2(react@16.14.0))(react@16.14.0):
    dependencies:
      clsx: 2.1.1
      dequal: 2.0.3
      make-cancellable-promise: 1.3.2
      make-event-props: 1.6.2
      merge-refs: 1.3.0(@types/react@19.1.12)
      pdfjs-dist: 4.3.136
      react: 16.14.0
      react-dom: 17.0.2(react@16.14.0)
      tiny-invariant: 1.3.3
      warning: 4.0.3
    optionalDependencies:
      '@types/react': 19.1.12
    transitivePeerDependencies:
      - encoding
      - supports-color

  react-textarea-autosize@8.3.3(@types/react@19.1.12)(react@16.14.0):
    dependencies:
      '@babel/runtime': 7.28.4
      react: 16.14.0
      use-composed-ref: 1.4.0(@types/react@19.1.12)(react@16.14.0)
      use-latest: 1.3.0(@types/react@19.1.12)(react@16.14.0)
    transitivePeerDependencies:
      - '@types/react'

  react-textarea-autosize@8.5.9(@types/react@19.1.12)(react@16.14.0):
    dependencies:
      '@babel/runtime': 7.28.4
      react: 16.14.0
      use-composed-ref: 1.4.0(@types/react@19.1.12)(react@16.14.0)
      use-latest: 1.3.0(@types/react@19.1.12)(react@16.14.0)
    transitivePeerDependencies:
      - '@types/react'

  react-transition-group@4.4.2(react-dom@17.0.2(react@16.14.0))(react@16.14.0):
    dependencies:
      '@babel/runtime': 7.28.4
      dom-helpers: 5.2.1
      loose-envify: 1.4.0
      prop-types: 15.8.1
      react: 16.14.0
      react-dom: 17.0.2(react@16.14.0)

  react-visibility-sensor@5.1.1(react-dom@17.0.2(react@16.14.0))(react@16.14.0):
    dependencies:
      prop-types: 15.8.1
      react: 16.14.0
      react-dom: 17.0.2(react@16.14.0)

  react@16.14.0:
    dependencies:
      loose-envify: 1.4.0
      object-assign: 4.1.1
      prop-types: 15.8.1

  reactcss@1.2.3(react@16.14.0):
    dependencies:
      lodash: 4.17.21
      react: 16.14.0

  readable-stream@2.3.8:
    dependencies:
      core-util-is: 1.0.3
      inherits: 2.0.4
      isarray: 1.0.0
      process-nextick-args: 2.0.1
      safe-buffer: 5.1.2
      string_decoder: 1.1.1
      util-deprecate: 1.0.2

  readable-stream@3.6.2:
    dependencies:
      inherits: 2.0.4
      string_decoder: 1.3.0
      util-deprecate: 1.0.2

  readdir-glob@1.1.3:
    dependencies:
      minimatch: 5.1.6

  redux@4.2.1:
    dependencies:
      '@babel/runtime': 7.28.4

  remove-accents@0.5.0: {}

  resize-observer-polyfill@1.5.1: {}

  rimraf@2.7.1:
    dependencies:
      glob: 7.2.3

  rimraf@3.0.2:
    dependencies:
      glob: 7.2.3
    optional: true

  rollup@4.50.0:
    dependencies:
      '@types/estree': 1.0.8
    optionalDependencies:
      '@rollup/rollup-android-arm-eabi': 4.50.0
      '@rollup/rollup-android-arm64': 4.50.0
      '@rollup/rollup-darwin-arm64': 4.50.0
      '@rollup/rollup-darwin-x64': 4.50.0
      '@rollup/rollup-freebsd-arm64': 4.50.0
      '@rollup/rollup-freebsd-x64': 4.50.0
      '@rollup/rollup-linux-arm-gnueabihf': 4.50.0
      '@rollup/rollup-linux-arm-musleabihf': 4.50.0
      '@rollup/rollup-linux-arm64-gnu': 4.50.0
      '@rollup/rollup-linux-arm64-musl': 4.50.0
      '@rollup/rollup-linux-loongarch64-gnu': 4.50.0
      '@rollup/rollup-linux-ppc64-gnu': 4.50.0
      '@rollup/rollup-linux-riscv64-gnu': 4.50.0
      '@rollup/rollup-linux-riscv64-musl': 4.50.0
      '@rollup/rollup-linux-s390x-gnu': 4.50.0
      '@rollup/rollup-linux-x64-gnu': 4.50.0
      '@rollup/rollup-linux-x64-musl': 4.50.0
      '@rollup/rollup-openharmony-arm64': 4.50.0
      '@rollup/rollup-win32-arm64-msvc': 4.50.0
      '@rollup/rollup-win32-ia32-msvc': 4.50.0
      '@rollup/rollup-win32-x64-msvc': 4.50.0
      fsevents: 2.3.3

  safe-buffer@5.1.2: {}

  safe-buffer@5.2.1: {}

  saxes@5.0.1:
    dependencies:
      xmlchars: 2.2.0

  scheduler@0.20.2:
    dependencies:
      loose-envify: 1.4.0
      object-assign: 4.1.1

  semver@6.3.1:
    optional: true

  semver@7.7.2:
    optional: true

  set-blocking@2.0.0:
    optional: true

  setimmediate@1.0.5: {}

  shebang-command@2.0.0:
    dependencies:
      shebang-regex: 3.0.0

  shebang-regex@3.0.0: {}

  signal-exit@3.0.7:
    optional: true

  simple-concat@1.0.1:
    optional: true

  simple-get@3.1.1:
    dependencies:
      decompress-response: 4.2.1
      once: 1.4.0
      simple-concat: 1.0.1
    optional: true

  smooth-signature@1.1.0: {}

  sortablejs@1.15.0: {}

  sortablejs@1.15.6: {}

  source-map-js@1.2.1: {}

  ssf@0.11.2:
    dependencies:
      frac: 1.1.2

  string-width@4.2.3:
    dependencies:
      emoji-regex: 8.0.0
      is-fullwidth-code-point: 3.0.0
      strip-ansi: 6.0.1
    optional: true

  string_decoder@1.1.1:
    dependencies:
      safe-buffer: 5.1.2

  string_decoder@1.3.0:
    dependencies:
      safe-buffer: 5.2.1

  strip-ansi@6.0.1:
    dependencies:
      ansi-regex: 5.0.1
    optional: true

  style-inject@0.3.0: {}

  tar-stream@2.2.0:
    dependencies:
      bl: 4.1.0
      end-of-stream: 1.4.5
      fs-constants: 1.0.0
      inherits: 2.0.4
      readable-stream: 3.6.2

  tar@6.2.1:
    dependencies:
      chownr: 2.0.0
      fs-minipass: 2.1.0
      minipass: 5.0.0
      minizlib: 2.1.2
      mkdirp: 1.0.4
      yallist: 4.0.0
    optional: true

  tiny-invariant@1.3.3: {}

  tinycolor2@1.6.0: {}

  tinyglobby@0.2.14:
    dependencies:
      fdir: 6.5.0(picomatch@4.0.3)
      picomatch: 4.0.3

  tinymce@6.8.6: {}

  tmp@0.2.5: {}

  tr46@0.0.3: {}

  traverse@0.3.9: {}

  tslib@2.3.0: {}

  tslib@2.8.1: {}

  ua-parser-js@1.0.41: {}

  uc.micro@1.0.6: {}

  uncontrollable@7.2.1(react@16.14.0):
    dependencies:
      '@babel/runtime': 7.28.4
      '@types/react': 19.1.12
      invariant: 2.2.4
      react: 16.14.0
      react-lifecycles-compat: 3.0.4

  unzipper@0.10.14:
    dependencies:
      big-integer: 1.6.52
      binary: 0.3.0
      bluebird: 3.4.7
      buffer-indexof-polyfill: 1.0.2
      duplexer2: 0.1.4
      fstream: 1.0.12
      graceful-fs: 4.2.11
      listenercount: 1.0.1
      readable-stream: 2.3.8
      setimmediate: 1.0.5

  use-composed-ref@1.4.0(@types/react@19.1.12)(react@16.14.0):
    dependencies:
      react: 16.14.0
    optionalDependencies:
      '@types/react': 19.1.12

  use-isomorphic-layout-effect@1.2.1(@types/react@19.1.12)(react@16.14.0):
    dependencies:
      react: 16.14.0
    optionalDependencies:
      '@types/react': 19.1.12

  use-latest@1.3.0(@types/react@19.1.12)(react@16.14.0):
    dependencies:
      react: 16.14.0
      use-isomorphic-layout-effect: 1.2.1(@types/react@19.1.12)(react@16.14.0)
    optionalDependencies:
      '@types/react': 19.1.12

  util-deprecate@1.0.2: {}

  uuid@8.3.2: {}

  video-react@0.15.0(react-dom@17.0.2(react@16.14.0))(react@16.14.0):
    dependencies:
      '@babel/runtime': 7.28.4
      classnames: 2.3.2
      lodash.throttle: 4.1.1
      prop-types: 15.8.1
      react: 16.14.0
      react-dom: 17.0.2(react@16.14.0)
      redux: 4.2.1

  vite@7.1.4:
    dependencies:
      esbuild: 0.25.9
      fdir: 6.5.0(picomatch@4.0.3)
      picomatch: 4.0.3
      postcss: 8.5.6
      rollup: 4.50.0
      tinyglobby: 0.2.14
    optionalDependencies:
      fsevents: 2.3.3

  vue-router@4.5.1(vue@3.5.21):
    dependencies:
      '@vue/devtools-api': 6.6.4
      vue: 3.5.21

  vue@3.5.21:
    dependencies:
      '@vue/compiler-dom': 3.5.21
      '@vue/compiler-sfc': 3.5.21
      '@vue/runtime-dom': 3.5.21
      '@vue/server-renderer': 3.5.21(vue@3.5.21)
      '@vue/shared': 3.5.21

  warning@4.0.3:
    dependencies:
      loose-envify: 1.4.0

  webidl-conversions@3.0.1: {}

  webworkify-webpack@https://codeload.github.com/xqq/webworkify-webpack/tar.gz/24d1e719b4a6cac37a518b2bb10fe124527ef4ef: {}

  whatwg-url@5.0.0:
    dependencies:
      tr46: 0.0.3
      webidl-conversions: 3.0.1

  which@2.0.2:
    dependencies:
      isexe: 2.0.0

  wide-align@1.1.5:
    dependencies:
      string-width: 4.2.3
    optional: true

  wmf@1.0.2: {}

  word@0.3.0: {}

  wrappy@1.0.2: {}

  xlsx@0.18.5:
    dependencies:
      adler-32: 1.3.1
      cfb: 1.2.2
      codepage: 1.15.0
      crc-32: 1.2.2
      ssf: 0.11.2
      wmf: 1.0.2
      word: 0.3.0

  xmlchars@2.2.0: {}

  yallist@4.0.0:
    optional: true

  zip-stream@4.1.1:
    dependencies:
      archiver-utils: 3.0.4
      compress-commons: 4.1.2
      readable-stream: 3.6.2

  zrender@5.6.0:
    dependencies:
      tslib: 2.3.0
