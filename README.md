# Amis 可视化编辑器

基于 Vue 3 + Vite + Amis 的可视化页面编辑器，支持拖拽式页面设计和实时预览。

## 功能特性

- 🎨 **可视化编辑**: 使用 Monaco Editor 提供强大的 JSON 配置编辑体验
- 👀 **实时预览**: 左侧编辑，右侧实时预览，所见即所得
- 💾 **配置保存**: 支持保存和加载页面配置到本地存储
- 📱 **响应式设计**: 支持各种屏幕尺寸的设备
- 🚀 **快速开始**: 内置示例页面，快速上手

## 技术栈

- **前端框架**: Vue 3 + Composition API
- **构建工具**: Vite
- **路由**: Vue Router 4
- **UI 框架**: Amis
- **代码编辑器**: Monaco Editor
- **包管理器**: pnpm

## 快速开始

### 安装依赖

```bash
pnpm install
```

### 启动开发服务器

```bash
pnpm dev
```

### 构建生产版本

```bash
pnpm build
```

### 预览生产版本

```bash
pnpm preview
```

## 使用说明

### 1. 首页
- 查看项目介绍和功能特性
- 浏览已保存的页面列表
- 快速访问编辑器和预览功能

### 2. 编辑器
- **左侧面板**: Monaco Editor JSON 配置编辑器
  - 支持语法高亮和错误检查
  - 自动格式化功能
  - 实时语法验证
- **右侧面板**: 实时预览
  - 自动更新（1秒延迟）
  - 手动刷新按钮
  - 错误提示显示

### 3. 页面管理
- **保存页面**: 为页面设置名称和描述
- **加载页面**: 从本地存储加载已保存的页面
- **删除页面**: 删除不需要的页面配置

### 4. 预览模式
- 全屏预览页面效果
- 支持直接跳转到编辑模式

## Amis 配置示例

### 基础页面结构
```json
{
  "type": "page",
  "title": "页面标题",
  "body": [
    // 页面内容组件
  ]
}
```

### 表单示例
```json
{
  "type": "form",
  "title": "用户表单",
  "body": [
    {
      "type": "input-text",
      "name": "username",
      "label": "用户名",
      "required": true
    },
    {
      "type": "input-email",
      "name": "email",
      "label": "邮箱"
    }
  ]
}
```

### 展示组件示例
```json
{
  "type": "card",
  "header": {
    "title": "卡片标题"
  },
  "body": "卡片内容"
}
```

## 项目结构

```
src/
├── components/          # Vue 组件
│   ├── Home.vue        # 首页组件
│   ├── AmisEditor.vue  # 编辑器组件
│   └── AmisPreview.vue # 预览组件
├── App.vue             # 根组件
├── main.js             # 入口文件
└── style.css           # 全局样式
```

## 开发指南

### 添加新的 Amis 组件
1. 在编辑器中编写 JSON 配置
2. 参考 [Amis 官方文档](https://aisuda.bce.baidu.com/amis/zh-CN/docs/index) 了解组件用法
3. 使用实时预览验证效果

### 自定义样式
- 修改 `src/style.css` 添加全局样式
- 在组件中使用 scoped 样式进行局部定制

## 相关链接

- [Amis 官方文档](https://aisuda.bce.baidu.com/amis/zh-CN/docs/index)
- [Vue 3 文档](https://v3.vuejs.org/)
- [Vite 文档](https://vitejs.dev/)
- [Monaco Editor](https://microsoft.github.io/monaco-editor/)

## 许可证

MIT License
