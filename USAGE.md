# 使用指南

## 快速开始

### 1. 访问首页
打开浏览器访问 `http://localhost:5174/`，你会看到：
- 项目介绍和功能特性
- 已保存的页面列表（初次使用会显示示例页面）
- 快速导航按钮

### 2. 使用编辑器
点击"开始编辑"按钮或导航栏的"编辑器"链接：

#### 左侧编辑面板
- 使用 Monaco Editor 编辑 JSON 配置
- 支持语法高亮、自动补全、错误检查
- 点击"格式化"按钮可以美化 JSON 代码

#### 右侧预览面板
- 实时显示页面效果
- 编辑后1秒自动更新预览
- 点击"刷新"按钮手动更新预览

### 3. 保存页面
1. 编辑完成后，点击"保存页面"按钮
2. 输入页面名称和描述
3. 点击"保存"确认

### 4. 预览页面
- 在首页点击已保存的页面卡片
- 或者在编辑器中点击"预览"按钮
- 支持全屏预览页面效果

## Amis 组件示例

### 基础文本显示
```json
{
  "type": "page",
  "title": "文本展示",
  "body": [
    {
      "type": "tpl",
      "tpl": "Hello, Amis!"
    }
  ]
}
```

### 表单组件
```json
{
  "type": "page",
  "title": "表单示例",
  "body": [
    {
      "type": "form",
      "title": "用户信息",
      "body": [
        {
          "type": "input-text",
          "name": "name",
          "label": "姓名",
          "required": true
        },
        {
          "type": "input-email",
          "name": "email",
          "label": "邮箱"
        },
        {
          "type": "select",
          "name": "city",
          "label": "城市",
          "options": [
            {"label": "北京", "value": "beijing"},
            {"label": "上海", "value": "shanghai"},
            {"label": "广州", "value": "guangzhou"}
          ]
        }
      ]
    }
  ]
}
```

### 卡片布局
```json
{
  "type": "page",
  "title": "卡片布局",
  "body": [
    {
      "type": "grid",
      "columns": [
        {
          "type": "card",
          "header": {
            "title": "卡片1",
            "subTitle": "副标题"
          },
          "body": "这是第一个卡片的内容"
        },
        {
          "type": "card",
          "header": {
            "title": "卡片2"
          },
          "body": "这是第二个卡片的内容"
        }
      ]
    }
  ]
}
```

### 数据展示
```json
{
  "type": "page",
  "title": "数据展示",
  "body": [
    {
      "type": "table",
      "data": {
        "items": [
          {"id": 1, "name": "张三", "age": 25, "city": "北京"},
          {"id": 2, "name": "李四", "age": 30, "city": "上海"},
          {"id": 3, "name": "王五", "age": 28, "city": "广州"}
        ]
      },
      "columns": [
        {"name": "id", "label": "ID"},
        {"name": "name", "label": "姓名"},
        {"name": "age", "label": "年龄"},
        {"name": "city", "label": "城市"}
      ]
    }
  ]
}
```

## 常用技巧

### 1. 快速复制示例
- 在首页查看示例页面
- 点击"编辑"按钮查看配置
- 复制配置代码到新页面

### 2. 调试技巧
- 使用浏览器开发者工具查看错误信息
- 检查 JSON 语法是否正确
- 参考右侧预览面板的错误提示

### 3. 组件嵌套
- 大部分组件都支持 `body` 属性来嵌套子组件
- 使用数组形式可以包含多个组件
- 注意组件的层级关系

### 4. 样式定制
- 使用 `className` 属性添加自定义样式类
- 支持 Bootstrap 样式类
- 可以使用内联样式 `style` 属性

## 常见问题

### Q: 预览不显示内容？
A: 检查 JSON 语法是否正确，确保所有括号和引号都匹配。

### Q: 如何添加更多组件？
A: 参考 [Amis 官方文档](https://aisuda.bce.baidu.com/amis/zh-CN/docs/index) 了解所有可用组件。

### Q: 页面保存在哪里？
A: 页面配置保存在浏览器的本地存储中，清除浏览器数据会丢失保存的页面。

### Q: 如何导出页面配置？
A: 可以复制编辑器中的 JSON 代码保存到文件中。

## 更多资源

- [Amis 官方文档](https://aisuda.bce.baidu.com/amis/zh-CN/docs/index)
- [Amis 组件示例](https://aisuda.bce.baidu.com/amis/zh-CN/components/page)
- [JSON 语法参考](https://www.json.org/json-zh.html)
